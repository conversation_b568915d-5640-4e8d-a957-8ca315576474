#############################################################################
# Makefile for building: sensuser.app/Contents/MacOS/sensuser
# Generated by qmake (3.1) (Qt 6.9.1)
# Project:  ../../sensuser.pro
# Template: app
# Command: /opt/Qt/6.9.1/macos/bin/qmake -o Makefile ../../sensuser.pro -spec macx-clang CONFIG-=qml_debug CONFIG-=qtquickcompiler CONFIG+=force_debug_info CONFIG+=separate_debug_info 'QMAKE_APPLE_DEVICE_ARCHS=x86_64 arm64'
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang
CXX           = /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++
DEFINES       = -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_OPENGLWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_OPENGL_LIB -DQT_GUI_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -O2 -g $(EXPORT_ARCH_ARGS) -isysroot /Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=12 -Wall -Wextra $(DEFINES)
CXXFLAGS      = -pipe -stdlib=libc++ -O2 -g -std=gnu++1z $(EXPORT_ARCH_ARGS) -isysroot /Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=12 -Wall -Wextra $(DEFINES)
INCPATH       = -I../../../sensuser -I. -I/usr/local/include/Eigen -I/opt/Qt/6.9.1/macos/lib/QtCharts.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtOpenGLWidgets.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtOpenGL.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers -I. -I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers -I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers -I. -I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers -I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers -I/opt/Qt/6.9.1/macos/mkspecs/macx-clang -F/opt/Qt/6.9.1/macos/lib
QMAKE         = /opt/Qt/6.9.1/macos/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /opt/Qt/6.9.1/macos/bin/qmake -install qinstall
QINSTALL_PROGRAM = /opt/Qt/6.9.1/macos/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = sensuser1.0.0
DISTDIR = /Users/<USER>/Desktop/projects/sensuser/repo/sensuser/build/Qt_6_9_1_for_macOS-Release/.tmp/sensuser1.0.0
LINK          = /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++
LFLAGS        = -stdlib=libc++ -headerpad_max_install_names $(EXPORT_ARCH_ARGS) -isysroot /Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -mmacosx-version-min=12 -Wl,-rpath,@executable_path/../Frameworks -Wl,-rpath,/opt/Qt/6.9.1/macos/lib
LIBS          = $(SUBLIBS) -F/opt/Qt/6.9.1/macos/lib -framework QtCharts -framework QtOpenGLWidgets -framework QtWidgets -framework QtOpenGL -framework QtGui -framework AppKit -framework ImageIO -framework Metal -framework QtCore -framework IOKit -framework DiskArbitration -framework UniformTypeIdentifiers -framework AGL -framework OpenGL   
AR            = /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar cq
RANLIB        = /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib -s
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = ../../main.cpp \
		../../mainwindow.cpp \
		../../mlp.cpp \
		../../layer.cpp \
		../../trainingworker.cpp \
		../../losscurvewidget.cpp qrc_qmake_qmake_qm_files.cpp \
		moc_mainwindow.cpp \
		moc_trainingworker.cpp \
		moc_losscurvewidget.cpp
OBJECTS       = main.o \
		mainwindow.o \
		mlp.o \
		layer.o \
		trainingworker.o \
		losscurvewidget.o \
		qrc_qmake_qmake_qm_files.o \
		moc_mainwindow.o \
		moc_trainingworker.o \
		moc_losscurvewidget.o
DIST          = /opt/Qt/6.9.1/macos/mkspecs/features/spec_pre.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/device_config.prf \
		/opt/Qt/6.9.1/macos/mkspecs/common/unix.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/mac.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/macx.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/sanitize.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/gcc-base.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/gcc-base-mac.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/clang.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/clang-mac.conf \
		/opt/Qt/6.9.1/macos/mkspecs/qconfig.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_freetype.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_libjpeg.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_libpng.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_openxr_loader.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_bluetooth.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_bodymovin_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_charts.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_charts_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_chartsqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_chartsqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_concurrent.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_concurrent_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_connectivity_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core5compat.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core5compat_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualization.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualization_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualizationqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualizationqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_dbus.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_dbus_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_designer.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_designer_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_example_icons_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_examples_asset_downloader_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_fb_support_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_ffmpegmediapluginimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_freetype_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphs.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphs_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphswidgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphswidgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpc.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpc_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpcquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpcquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_gui.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_gui_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_harfbuzz_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_help.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_help_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_httpserver.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_httpserver_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_jpeg_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_jsonrpc_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsanimation.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsanimation_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsfolderlistmodel.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsfolderlistmodel_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsplatform.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsplatform_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsqmlmodels.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsqmlmodels_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssettings.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssettings_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssharedimage.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssharedimage_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labswavefrontmesh.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labswavefrontmesh_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_languageserver_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_linguist.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_location.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_location_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimedia.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimedia_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediaquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediatestlibprivate_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_network.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_network_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_networkauth.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_networkauth_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_nfc.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_nfc_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_opengl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_opengl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_openglwidgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_openglwidgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_png_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioning.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioning_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioningquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_printsupport.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_printsupport_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobuf.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobuf_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtcoretypes.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtcoretypes_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtguitypes.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtguitypes_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufwellknowntypes.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufwellknowntypes_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qdoccatch_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qdoccatchconversions_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qdoccatchgenerators_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlassetdownloader.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlassetdownloader_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcompiler.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcompiler_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcore.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcore_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmldom_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlformat_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlintegration.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlintegration_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmllocalstorage.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmllocalstorage_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlls_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmeta.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmeta_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmodels.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlnetwork.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlnetwork_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltest.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltest_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltoolingsettings_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltyperegistrar_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlxmllistmodel.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlxmllistmodel_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3d.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3d_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetimport.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetimport_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetutils.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetutils_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3deffects.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3deffects_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dglslparser_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpers.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpers_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpersimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpersimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3diblbaker.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3diblbaker_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticleeffects.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticleeffects_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticles.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticles_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysics.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysics_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysicshelpers.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysicshelpers_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3druntimerender.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3druntimerender_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dspatialaudio_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dutils.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dutils_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dxr.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dxr_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basic.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basic_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusion.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusion_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imagine.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imagine_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2impl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2impl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2iosstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2iosstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2macosstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2macosstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2material.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2material_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universal.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universal_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrolstestutilsprivate_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2quickimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2quickimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2utils.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2utils_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickeffects.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickeffects_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicklayouts.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicklayouts_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktestutilsprivate_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimeline.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimeline_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimelineblendtrees.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimelineblendtrees_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickvectorimage.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickvectorimage_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickvectorimagegenerator_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickwidgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjects.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjectsqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjectsqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_repparser.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_repparser_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxmlglobal_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxmlqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxmlqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensors.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensors_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensorsquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensorsquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialbus.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialbus_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialport.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialport_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_shadertools.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_shadertools_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_spatialaudio.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_spatialaudio_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sql.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sql_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachine.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachine_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachineqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachineqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svg.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svg_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svgwidgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svgwidgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_testinternals_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_testlib.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_testlib_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_texttospeech.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_tools_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_uiplugin.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_uitools.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_uitools_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardsettings.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardsettings_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannel.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannel_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannelquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannelquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_websockets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_websockets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webview.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webview_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webviewquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webviewquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_widgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_widgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_xml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_xml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwinbluetoothpermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwincalendarpermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwincamerapermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwincontactspermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwinlocationpermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwinmicrophonepermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/features/qt_functions.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/qt_config.prf \
		/opt/Qt/6.9.1/macos/mkspecs/macx-clang/qmake.conf \
		/opt/Qt/6.9.1/macos/mkspecs/features/spec_post.prf \
		../../../../.qmake.stash \
		/opt/Qt/6.9.1/macos/mkspecs/features/exclusive_builds.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/sdk.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/toolchain.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/toolchain.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/default_pre.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/default_pre.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/resolve_config.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/default_post.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/default_post.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/objective_c.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/lrelease.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/resolve_target.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/unix/separate_debug_info.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/mac.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/warn_on.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/permissions.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/qt.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/resources_functions.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/resources.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/moc.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/unix/opengl.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/uic.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/unix/thread.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/qmake_use.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/file_copies.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/rez.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/asset_catalogs.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/testcase_targets.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/exceptions.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/yacc.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/lex.prf \
		../../../../sensuser.pro ../../mainwindow.h \
		../../mlp.h \
		../../layer.h \
		../../trainingworker.h \
		../../losscurvewidget.h \
		../../optimizertypes.h ../../main.cpp \
		../../mainwindow.cpp \
		../../mlp.cpp \
		../../layer.cpp \
		../../trainingworker.cpp \
		../../losscurvewidget.cpp
QMAKE_TARGET  = sensuser
DESTDIR       = 
TARGET        = sensuser.app/Contents/MacOS/sensuser

####### Custom Variables
EXPORT_QMAKE_MAC_SDK = macosx
EXPORT_QMAKE_MAC_SDK_VERSION = 15.5
EXPORT_QMAKE_XCODE_DEVELOPER_PATH = /Applications/Xcode.app/Contents/Developer
EXPORT__QMAKE_STASH_ = /Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qmake.stash
EXPORT_VALID_ARCHS = x86_64 arm64
EXPORT_DEFAULT_ARCHS = x86_64 arm64
EXPORT_ARCHS = $(filter $(EXPORT_VALID_ARCHS), $(if $(ARCHS), $(ARCHS), $(if $(EXPORT_DEFAULT_ARCHS), $(EXPORT_DEFAULT_ARCHS), $(EXPORT_VALID_ARCHS))))
EXPORT_ARCH_ARGS = $(foreach arch, $(if $(EXPORT_ARCHS), $(EXPORT_ARCHS), $(EXPORT_VALID_ARCHS)), -arch $(arch))
EXPORT__PRO_FILE_ = /Users/<USER>/Desktop/projects/sensuser/repo/sensuser/sensuser.pro


include /opt/Qt/6.9.1/macos/mkspecs/features/mac/sdk.mk
first: all
####### Build rules

sensuser.app/Contents/MacOS/sensuser: ui_mainwindow.h $(OBJECTS)  
	@test -d sensuser.app/Contents/MacOS/ || mkdir -p sensuser.app/Contents/MacOS/
	$(LINK) $(LFLAGS) -o $(TARGET)  $(OBJECTS) $(OBJCOMP) $(LIBS)
	mkdir -p /Users/<USER>/Desktop/projects/sensuser/repo/sensuser/build/Qt_6_9_1_for_macOS-Release/sensuser.app.dSYM/Contents/Resources/DWARF && dsymutil sensuser.app/Contents/MacOS/sensuser --flat -o sensuser.app.dSYM/Contents/Resources/DWARF/sensuser && strip -S sensuser.app/Contents/MacOS/sensuser

Makefile: ../../sensuser.pro /opt/Qt/6.9.1/macos/mkspecs/macx-clang/qmake.conf /opt/Qt/6.9.1/macos/mkspecs/features/spec_pre.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/device_config.prf \
		/opt/Qt/6.9.1/macos/mkspecs/common/unix.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/mac.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/macx.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/sanitize.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/gcc-base.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/gcc-base-mac.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/clang.conf \
		/opt/Qt/6.9.1/macos/mkspecs/common/clang-mac.conf \
		/opt/Qt/6.9.1/macos/mkspecs/qconfig.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_freetype.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_libjpeg.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_libpng.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_openxr_loader.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_bluetooth.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_bodymovin_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_charts.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_charts_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_chartsqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_chartsqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_concurrent.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_concurrent_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_connectivity_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core5compat.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core5compat_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualization.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualization_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualizationqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualizationqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_dbus.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_dbus_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_designer.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_designer_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_example_icons_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_examples_asset_downloader_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_fb_support_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_ffmpegmediapluginimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_freetype_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphs.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphs_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphswidgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphswidgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpc.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpc_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpcquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpcquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_gui.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_gui_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_harfbuzz_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_help.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_help_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_httpserver.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_httpserver_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_jpeg_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_jsonrpc_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsanimation.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsanimation_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsfolderlistmodel.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsfolderlistmodel_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsplatform.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsplatform_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsqmlmodels.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsqmlmodels_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssettings.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssettings_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssharedimage.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssharedimage_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labswavefrontmesh.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labswavefrontmesh_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_languageserver_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_linguist.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_location.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_location_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimedia.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimedia_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediaquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediatestlibprivate_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_network.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_network_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_networkauth.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_networkauth_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_nfc.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_nfc_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_opengl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_opengl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_openglwidgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_openglwidgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_png_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioning.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioning_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioningquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_printsupport.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_printsupport_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobuf.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobuf_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtcoretypes.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtcoretypes_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtguitypes.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtguitypes_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufwellknowntypes.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufwellknowntypes_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qdoccatch_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qdoccatchconversions_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qdoccatchgenerators_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlassetdownloader.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlassetdownloader_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcompiler.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcompiler_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcore.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcore_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmldom_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlformat_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlintegration.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlintegration_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmllocalstorage.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmllocalstorage_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlls_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmeta.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmeta_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmodels.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlnetwork.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlnetwork_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltest.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltest_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltoolingsettings_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltyperegistrar_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlxmllistmodel.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlxmllistmodel_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3d.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3d_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetimport.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetimport_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetutils.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetutils_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3deffects.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3deffects_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dglslparser_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpers.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpers_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpersimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpersimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3diblbaker.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3diblbaker_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticleeffects.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticleeffects_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticles.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticles_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysics.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysics_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysicshelpers.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysicshelpers_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3druntimerender.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3druntimerender_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dspatialaudio_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dutils.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dutils_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dxr.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dxr_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basic.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basic_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusion.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusion_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imagine.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imagine_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2impl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2impl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2iosstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2iosstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2macosstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2macosstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2material.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2material_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universal.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universal_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrolstestutilsprivate_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2quickimpl.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2quickimpl_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2utils.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2utils_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickeffects.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickeffects_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicklayouts.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicklayouts_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktestutilsprivate_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimeline.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimeline_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimelineblendtrees.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimelineblendtrees_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickvectorimage.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickvectorimage_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickvectorimagegenerator_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickwidgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjects.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjectsqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjectsqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_repparser.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_repparser_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxmlglobal_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxmlqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxmlqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensors.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensors_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensorsquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensorsquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialbus.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialbus_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialport.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialport_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_shadertools.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_shadertools_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_spatialaudio.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_spatialaudio_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sql.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sql_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachine.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachine_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachineqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachineqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svg.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svg_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svgwidgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svgwidgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_testinternals_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_testlib.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_testlib_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_texttospeech.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_tools_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_uiplugin.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_uitools.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_uitools_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardqml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardqml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardsettings.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardsettings_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannel.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannel_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannelquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannelquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_websockets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_websockets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webview.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webview_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webviewquick.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webviewquick_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_widgets.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_widgets_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_xml.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_xml_private.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwinbluetoothpermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwincalendarpermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwincamerapermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwincontactspermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwinlocationpermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwinmicrophonepermission.pri \
		/opt/Qt/6.9.1/macos/mkspecs/features/qt_functions.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/qt_config.prf \
		/opt/Qt/6.9.1/macos/mkspecs/macx-clang/qmake.conf \
		/opt/Qt/6.9.1/macos/mkspecs/features/spec_post.prf \
		../../.qmake.stash \
		/opt/Qt/6.9.1/macos/mkspecs/features/exclusive_builds.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/sdk.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/toolchain.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/toolchain.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/default_pre.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/default_pre.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/resolve_config.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/default_post.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/default_post.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/objective_c.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/lrelease.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/resolve_target.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/unix/separate_debug_info.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/mac.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/warn_on.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/permissions.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/qt.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/resources_functions.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/resources.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/moc.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/unix/opengl.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/uic.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/unix/thread.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/qmake_use.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/file_copies.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/rez.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/mac/asset_catalogs.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/testcase_targets.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/exceptions.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/yacc.prf \
		/opt/Qt/6.9.1/macos/mkspecs/features/lex.prf \
		../../sensuser.pro \
		/opt/Qt/6.9.1/macos/mkspecs/macx-clang/Info.plist.dSYM.in \
		qmake_qmake_qm_files.qrc \
		/opt/Qt/6.9.1/macos/lib/QtCharts.framework/Resources/QtCharts.prl \
		/opt/Qt/6.9.1/macos/lib/QtOpenGLWidgets.framework/Resources/QtOpenGLWidgets.prl \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Resources/QtWidgets.prl \
		/opt/Qt/6.9.1/macos/lib/QtOpenGL.framework/Resources/QtOpenGL.prl \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Resources/QtGui.prl \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Resources/QtCore.prl
	$(QMAKE) -o Makefile ../../sensuser.pro -spec macx-clang CONFIG-=qml_debug CONFIG-=qtquickcompiler CONFIG+=force_debug_info CONFIG+=separate_debug_info 'QMAKE_APPLE_DEVICE_ARCHS=x86_64 arm64'
/opt/Qt/6.9.1/macos/mkspecs/features/spec_pre.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/device_config.prf:
/opt/Qt/6.9.1/macos/mkspecs/common/unix.conf:
/opt/Qt/6.9.1/macos/mkspecs/common/mac.conf:
/opt/Qt/6.9.1/macos/mkspecs/common/macx.conf:
/opt/Qt/6.9.1/macos/mkspecs/common/sanitize.conf:
/opt/Qt/6.9.1/macos/mkspecs/common/gcc-base.conf:
/opt/Qt/6.9.1/macos/mkspecs/common/gcc-base-mac.conf:
/opt/Qt/6.9.1/macos/mkspecs/common/clang.conf:
/opt/Qt/6.9.1/macos/mkspecs/common/clang-mac.conf:
/opt/Qt/6.9.1/macos/mkspecs/qconfig.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_freetype.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_libjpeg.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_libpng.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_ext_openxr_loader.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_bluetooth.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_bluetooth_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_bodymovin_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_charts.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_charts_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_chartsqml.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_chartsqml_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_concurrent.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_concurrent_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_connectivity_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core5compat.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core5compat_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_core_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualization.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualization_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualizationqml.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_datavisualizationqml_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_dbus.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_dbus_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_designer.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_designer_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_designercomponents_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_example_icons_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_examples_asset_downloader_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_fb_support_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_ffmpegmediapluginimpl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_freetype_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphs.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphs_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphswidgets.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_graphswidgets_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpc.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpc_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpcquick.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_grpcquick_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_gui.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_gui_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_harfbuzz_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_help.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_help_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_httpserver.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_httpserver_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_jpeg_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_jsonrpc_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsanimation.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsanimation_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsfolderlistmodel.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsfolderlistmodel_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsplatform.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsplatform_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsqmlmodels.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labsqmlmodels_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssettings.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssettings_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssharedimage.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labssharedimage_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labswavefrontmesh.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_labswavefrontmesh_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_languageserver_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_linguist.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_location.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_location_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimedia.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimedia_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediaquick_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediatestlibprivate_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediawidgets.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_network.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_network_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_networkauth.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_networkauth_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_nfc.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_nfc_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_opengl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_opengl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_openglwidgets.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_openglwidgets_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_packetprotocol_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_png_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioning.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioning_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioningquick.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_positioningquick_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_printsupport.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_printsupport_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobuf.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobuf_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtcoretypes.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtcoretypes_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtguitypes.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufqtguitypes_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufquick.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufquick_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufwellknowntypes.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_protobufwellknowntypes_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qdoccatch_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qdoccatchconversions_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qdoccatchgenerators_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qml.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qml_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlassetdownloader.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlassetdownloader_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcompiler.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcompiler_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcore.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlcore_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmldebug_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmldom_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlformat_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlintegration.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlintegration_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmllocalstorage.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmllocalstorage_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlls_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmeta.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmeta_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmodels.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlmodels_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlnetwork.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlnetwork_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltest.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltest_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltoolingsettings_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmltyperegistrar_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlworkerscript.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlworkerscript_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlxmllistmodel.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_qmlxmllistmodel_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3d.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3d_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetimport.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetimport_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetutils.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dassetutils_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3deffects.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3deffects_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dglslparser_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpers.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpers_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpersimpl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dhelpersimpl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3diblbaker.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3diblbaker_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticleeffects.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticleeffects_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticles.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dparticles_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysics.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysics_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysicshelpers.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dphysicshelpers_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3druntimerender.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3druntimerender_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dspatialaudio_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dutils.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dutils_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dxr.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick3dxr_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quick_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basic.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basic_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2basicstyleimpl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusion.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusion_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2fusionstyleimpl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imagine.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imagine_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2imaginestyleimpl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2impl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2impl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2iosstyleimpl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2iosstyleimpl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2macosstyleimpl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2macosstyleimpl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2material.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2material_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2materialstyleimpl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universal.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universal_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrols2universalstyleimpl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickcontrolstestutilsprivate_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2quickimpl.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2quickimpl_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2utils.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickdialogs2utils_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickeffects.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickeffects_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicklayouts.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicklayouts_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickparticles_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickshapes_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktemplates2.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktestutilsprivate_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimeline.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimeline_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimelineblendtrees.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quicktimelineblendtrees_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickvectorimage.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickvectorimage_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickvectorimagegenerator_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickwidgets.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_quickwidgets_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjects.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjects_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjectsqml.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_remoteobjectsqml_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_repparser.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_repparser_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxml.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxml_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxmlglobal_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxmlqml.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_scxmlqml_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensors.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensors_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensorsquick.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sensorsquick_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialbus.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialbus_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialport.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_serialport_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_shadertools.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_shadertools_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_spatialaudio.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_spatialaudio_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sql.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_sql_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachine.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachine_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachineqml.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_statemachineqml_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svg.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svg_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svgwidgets.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_svgwidgets_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_testinternals_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_testlib.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_testlib_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_texttospeech.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_texttospeech_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_tools_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_uiplugin.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_uitools.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_uitools_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboard.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboard_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardqml.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardqml_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardsettings.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_virtualkeyboardsettings_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannel.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannel_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannelquick.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webchannelquick_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_websockets.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_websockets_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webview.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webview_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webviewquick.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_webviewquick_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_widgets.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_widgets_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_xml.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_lib_xml_private.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwinbluetoothpermission.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwincalendarpermission.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwincamerapermission.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwincontactspermission.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwinlocationpermission.pri:
/opt/Qt/6.9.1/macos/mkspecs/modules/qt_plugin_qdarwinmicrophonepermission.pri:
/opt/Qt/6.9.1/macos/mkspecs/features/qt_functions.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/qt_config.prf:
/opt/Qt/6.9.1/macos/mkspecs/macx-clang/qmake.conf:
/opt/Qt/6.9.1/macos/mkspecs/features/spec_post.prf:
../../.qmake.stash:
/opt/Qt/6.9.1/macos/mkspecs/features/exclusive_builds.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/mac/sdk.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/toolchain.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/mac/toolchain.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/default_pre.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/mac/default_pre.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/resolve_config.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/default_post.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/mac/default_post.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/mac/objective_c.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/lrelease.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/resolve_target.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/unix/separate_debug_info.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/mac/mac.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/warn_on.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/permissions.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/qt.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/resources_functions.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/resources.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/moc.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/unix/opengl.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/uic.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/unix/thread.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/qmake_use.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/file_copies.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/mac/rez.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/mac/asset_catalogs.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/testcase_targets.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/exceptions.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/yacc.prf:
/opt/Qt/6.9.1/macos/mkspecs/features/lex.prf:
../../sensuser.pro:
/opt/Qt/6.9.1/macos/mkspecs/macx-clang/Info.plist.dSYM.in:
qmake_qmake_qm_files.qrc:
/opt/Qt/6.9.1/macos/lib/QtCharts.framework/Resources/QtCharts.prl:
/opt/Qt/6.9.1/macos/lib/QtOpenGLWidgets.framework/Resources/QtOpenGLWidgets.prl:
/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Resources/QtWidgets.prl:
/opt/Qt/6.9.1/macos/lib/QtOpenGL.framework/Resources/QtOpenGL.prl:
/opt/Qt/6.9.1/macos/lib/QtGui.framework/Resources/QtGui.prl:
/opt/Qt/6.9.1/macos/lib/QtCore.framework/Resources/QtCore.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile ../../sensuser.pro -spec macx-clang CONFIG-=qml_debug CONFIG-=qtquickcompiler CONFIG+=force_debug_info CONFIG+=separate_debug_info 'QMAKE_APPLE_DEVICE_ARCHS=x86_64 arm64'

qmake_all: FORCE

sensuser.app/Contents/PkgInfo: 
	@test -d sensuser.app/Contents || mkdir -p sensuser.app/Contents
	@$(DEL_FILE) sensuser.app/Contents/PkgInfo
	@echo "APPL????" > sensuser.app/Contents/PkgInfo
sensuser.app/Contents/Info.plist: 
	@test -d sensuser.app/Contents || mkdir -p sensuser.app/Contents
	@$(DEL_FILE) sensuser.app/Contents/Info.plist
	@plutil -convert xml1 -o - /opt/Qt/6.9.1/macos/mkspecs/macx-clang/Info.plist.app | sed -e "s,@SHORT_VERSION@,1.0,g" -e "s,\$${QMAKE_SHORT_VERSION},1.0,g" -e "s,@FULL_VERSION@,1.0.0,g" -e "s,\$${QMAKE_FULL_VERSION},1.0.0,g" -e "s,@TYPEINFO@,????,g" -e "s,\$${QMAKE_PKGINFO_TYPEINFO},????,g" -e "s,@BUNDLEIDENTIFIER@,com.andrewjosephsmith.noodlenet-cpp.sensuser,g" -e "s,\$${PRODUCT_BUNDLE_IDENTIFIER},com.andrewjosephsmith.noodlenet-cpp.sensuser,g" -e "s,\$${MACOSX_DEPLOYMENT_TARGET},12,g" -e "s,\$${IPHONEOS_DEPLOYMENT_TARGET},,g" -e "s,\$${TVOS_DEPLOYMENT_TARGET},,g" -e "s,\$${WATCHOS_DEPLOYMENT_TARGET},,g" -e "s,\$${IOS_LAUNCH_SCREEN},LaunchScreen,g" -e "s,@ICON@,,g" -e "s,\$${ASSETCATALOG_COMPILER_APPICON_NAME},,g" -e "s,@EXECUTABLE@,sensuser,g" -e "s,@LIBRARY@,sensuser,g" -e "s,\$${EXECUTABLE_NAME},sensuser,g" -e "s,@TYPEINFO@,????,g" -e "s,\$${QMAKE_PKGINFO_TYPEINFO},????,g" >sensuser.app/Contents/Info.plist

all: Makefile \
		sensuser.app/Contents/PkgInfo \
		sensuser.app/Contents/Info.plist sensuser.app/Contents/MacOS/sensuser

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents ../../sensuser_en_US.ts $(DISTDIR)/
	$(COPY_FILE) --parents qmake_qmake_qm_files.qrc $(DISTDIR)/
	$(COPY_FILE) --parents ../../mainwindow.h ../../mlp.h ../../layer.h ../../trainingworker.h ../../losscurvewidget.h ../../optimizertypes.h $(DISTDIR)/
	$(COPY_FILE) --parents ../../main.cpp ../../mainwindow.cpp ../../mlp.cpp ../../layer.cpp ../../trainingworker.cpp ../../losscurvewidget.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../../mainwindow.ui $(DISTDIR)/
	$(COPY_FILE) --parents ../../sensuser_en_US.ts $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) -r sensuser.app
	-$(DEL_FILE) /Users/<USER>/Desktop/projects/sensuser/repo/sensuser/build/Qt_6_9_1_for_macOS-Release/sensuser.app.dSYM/Contents/Resources/DWARF/sensuser /Users/<USER>/Desktop/projects/sensuser/repo/sensuser/build/Qt_6_9_1_for_macOS-Release/sensuser.app.dSYM/Contents/Info.plist
	-$(DEL_FILE) Makefile


####### Sub-libraries

xcodeproj:
	@$(QMAKE) -spec macx-xcode "$(EXPORT__PRO_FILE_)" -spec macx-clang CONFIG-=qml_debug CONFIG-=qtquickcompiler CONFIG+=force_debug_info CONFIG+=separate_debug_info QMAKE_APPLE_DEVICE_ARCHS=x86_64 arm64

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_lrelease_make_all: .qm/sensuser_en_US.qm
compiler_lrelease_clean:
	-$(DEL_FILE) .qm/sensuser_en_US.qm
.qm/sensuser_en_US.qm: ../../sensuser_en_US.ts
	/opt/Qt/6.9.1/macos/bin/lrelease ../../sensuser_en_US.ts -qm .qm/sensuser_en_US.qm

compiler_rcc_make_all: qrc_qmake_qmake_qm_files.cpp
compiler_rcc_clean:
	-$(DEL_FILE) qrc_qmake_qmake_qm_files.cpp
qrc_qmake_qmake_qm_files.cpp: qmake_qmake_qm_files.qrc \
		/opt/Qt/6.9.1/macos/libexec/rcc \
		.qm/sensuser_en_US.qm
	/opt/Qt/6.9.1/macos/libexec/rcc -name qmake_qmake_qm_files --no-zstd qmake_qmake_qm_files.qrc -o qrc_qmake_qmake_qm_files.cpp

compiler_moc_header_make_all: moc_mainwindow.cpp moc_trainingworker.cpp moc_losscurvewidget.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_mainwindow.cpp moc_trainingworker.cpp moc_losscurvewidget.cpp
moc_mainwindow.cpp: ../../mainwindow.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QMainWindow \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qmainwindow.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QThread \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qthread.h \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/QImage \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/qimage.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QFileDialog \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qfiledialog.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QMessageBox \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qmessagebox.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonDocument \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsondocument.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonObject \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsonobject.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QFile \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qfile.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDir \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdir.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QGraphicsScene \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qgraphicsscene.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QGraphicsPixmapItem \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qgraphicsitem.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QTimer \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qtimer.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QListWidget \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qlistwidget.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QSpinBox \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qspinbox.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QComboBox \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qcombobox.h \
		../../mlp.h \
		../../layer.h \
		/usr/local/include/Eigen/Dense \
		/usr/local/include/Eigen/Core \
		/usr/local/include/Eigen/src/Core/util/DisableStupidWarnings.h \
		/usr/local/include/Eigen/src/Core/util/Macros.h \
		/usr/local/include/Eigen/src/Core/util/ConfigureVectorization.h \
		/usr/local/include/Eigen/src/Core/util/MKL_support.h \
		/usr/local/include/Eigen/src/misc/blas.h \
		/usr/local/include/Eigen/src/Core/util/Constants.h \
		/usr/local/include/Eigen/src/Core/util/Meta.h \
		/usr/local/include/Eigen/src/Core/util/ForwardDeclarations.h \
		/usr/local/include/Eigen/src/Core/util/StaticAssert.h \
		/usr/local/include/Eigen/src/Core/util/XprHelper.h \
		/usr/local/include/Eigen/src/Core/util/Memory.h \
		/usr/local/include/Eigen/src/Core/util/IntegralConstant.h \
		/usr/local/include/Eigen/src/Core/util/SymbolicIndex.h \
		/usr/local/include/Eigen/src/Core/NumTraits.h \
		/usr/local/include/Eigen/src/Core/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/GenericPacketMath.h \
		/usr/local/include/Eigen/src/Core/MathFunctionsImpl.h \
		/usr/local/include/Eigen/src/Core/arch/Default/ConjHelper.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Half.h \
		/usr/local/include/Eigen/src/Core/arch/Default/BFloat16.h \
		/usr/local/include/Eigen/src/Core/arch/Default/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctionsFwd.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/SyclMemoryModel.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/InteropHeaders.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Settings.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctions.h \
		/usr/local/include/Eigen/src/Core/functors/TernaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/BinaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/UnaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/NullaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/StlFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/AssignmentFunctors.h \
		/usr/local/include/Eigen/src/Core/arch/CUDA/Complex.h \
		/usr/local/include/Eigen/src/Core/util/IndexedViewHelper.h \
		/usr/local/include/Eigen/src/Core/util/ReshapedHelper.h \
		/usr/local/include/Eigen/src/Core/ArithmeticSequence.h \
		/usr/local/include/Eigen/src/Core/IO.h \
		/usr/local/include/Eigen/src/Core/DenseCoeffsBase.h \
		/usr/local/include/Eigen/src/Core/DenseBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/BlockMethods.h \
		/usr/local/include/Eigen/src/plugins/IndexedViewMethods.h \
		/usr/local/include/Eigen/src/plugins/ReshapedMethods.h \
		/usr/local/include/Eigen/src/Core/MatrixBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/EigenBase.h \
		/usr/local/include/Eigen/src/Core/Product.h \
		/usr/local/include/Eigen/src/Core/CoreEvaluators.h \
		/usr/local/include/Eigen/src/Core/AssignEvaluator.h \
		/usr/local/include/Eigen/src/Core/Assign.h \
		/usr/local/include/Eigen/src/Core/ArrayBase.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/util/BlasUtil.h \
		/usr/local/include/Eigen/src/Core/DenseStorage.h \
		/usr/local/include/Eigen/src/Core/NestByValue.h \
		/usr/local/include/Eigen/src/Core/ReturnByValue.h \
		/usr/local/include/Eigen/src/Core/NoAlias.h \
		/usr/local/include/Eigen/src/Core/PlainObjectBase.h \
		/usr/local/include/Eigen/src/Core/Matrix.h \
		/usr/local/include/Eigen/src/Core/Array.h \
		/usr/local/include/Eigen/src/Core/CwiseTernaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseNullaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryView.h \
		/usr/local/include/Eigen/src/Core/SelfCwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/Dot.h \
		/usr/local/include/Eigen/src/Core/StableNorm.h \
		/usr/local/include/Eigen/src/Core/Stride.h \
		/usr/local/include/Eigen/src/Core/MapBase.h \
		/usr/local/include/Eigen/src/Core/Map.h \
		/usr/local/include/Eigen/src/Core/Ref.h \
		/usr/local/include/Eigen/src/Core/Block.h \
		/usr/local/include/Eigen/src/Core/VectorBlock.h \
		/usr/local/include/Eigen/src/Core/IndexedView.h \
		/usr/local/include/Eigen/src/Core/Reshaped.h \
		/usr/local/include/Eigen/src/Core/Transpose.h \
		/usr/local/include/Eigen/src/Core/DiagonalMatrix.h \
		/usr/local/include/Eigen/src/Core/Diagonal.h \
		/usr/local/include/Eigen/src/Core/DiagonalProduct.h \
		/usr/local/include/Eigen/src/Core/Redux.h \
		/usr/local/include/Eigen/src/Core/Visitor.h \
		/usr/local/include/Eigen/src/Core/Fuzzy.h \
		/usr/local/include/Eigen/src/Core/Swap.h \
		/usr/local/include/Eigen/src/Core/CommaInitializer.h \
		/usr/local/include/Eigen/src/Core/GeneralProduct.h \
		/usr/local/include/Eigen/src/Core/Solve.h \
		/usr/local/include/Eigen/src/Core/Inverse.h \
		/usr/local/include/Eigen/src/Core/SolverBase.h \
		/usr/local/include/Eigen/src/Core/PermutationMatrix.h \
		/usr/local/include/Eigen/src/Core/Transpositions.h \
		/usr/local/include/Eigen/src/Core/TriangularMatrix.h \
		/usr/local/include/Eigen/src/Core/SelfAdjointView.h \
		/usr/local/include/Eigen/src/Core/products/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/products/Parallelizer.h \
		/usr/local/include/Eigen/src/Core/ProductEvaluators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/SolveTriangular.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointProduct.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointRank2Update.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverVector.h \
		/usr/local/include/Eigen/src/Core/BandMatrix.h \
		/usr/local/include/Eigen/src/Core/CoreIterators.h \
		/usr/local/include/Eigen/src/Core/ConditionEstimator.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProduct.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductCommon.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductMMA.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/BooleanRedux.h \
		/usr/local/include/Eigen/src/Core/Select.h \
		/usr/local/include/Eigen/src/Core/VectorwiseOp.h \
		/usr/local/include/Eigen/src/Core/PartialReduxEvaluator.h \
		/usr/local/include/Eigen/src/Core/Random.h \
		/usr/local/include/Eigen/src/Core/Replicate.h \
		/usr/local/include/Eigen/src/Core/Reverse.h \
		/usr/local/include/Eigen/src/Core/ArrayWrapper.h \
		/usr/local/include/Eigen/src/Core/StlIterators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/Assign_MKL.h \
		/usr/local/include/Eigen/src/Core/GlobalFunctions.h \
		/usr/local/include/Eigen/src/Core/util/ReenableStupidWarnings.h \
		/usr/local/include/Eigen/LU \
		/usr/local/include/Eigen/src/misc/Kernel.h \
		/usr/local/include/Eigen/src/misc/Image.h \
		/usr/local/include/Eigen/src/LU/FullPivLU.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU.h \
		/usr/local/include/Eigen/src/misc/lapacke.h \
		/usr/local/include/Eigen/src/misc/lapacke_mangling.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU_LAPACKE.h \
		/usr/local/include/Eigen/src/LU/Determinant.h \
		/usr/local/include/Eigen/src/LU/InverseImpl.h \
		/usr/local/include/Eigen/src/LU/arch/InverseSize4.h \
		/usr/local/include/Eigen/Cholesky \
		/usr/local/include/Eigen/Jacobi \
		/usr/local/include/Eigen/src/Jacobi/Jacobi.h \
		/usr/local/include/Eigen/src/Cholesky/LLT.h \
		/usr/local/include/Eigen/src/Cholesky/LDLT.h \
		/usr/local/include/Eigen/src/Cholesky/LLT_LAPACKE.h \
		/usr/local/include/Eigen/QR \
		/usr/local/include/Eigen/Householder \
		/usr/local/include/Eigen/src/Householder/Householder.h \
		/usr/local/include/Eigen/src/Householder/HouseholderSequence.h \
		/usr/local/include/Eigen/src/Householder/BlockHouseholder.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR.h \
		/usr/local/include/Eigen/src/QR/FullPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/CompleteOrthogonalDecomposition.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/SVD \
		/usr/local/include/Eigen/src/misc/RealSvd2x2.h \
		/usr/local/include/Eigen/src/SVD/UpperBidiagonalization.h \
		/usr/local/include/Eigen/src/SVD/SVDBase.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD.h \
		/usr/local/include/Eigen/src/SVD/BDCSVD.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD_LAPACKE.h \
		/usr/local/include/Eigen/Geometry \
		/usr/local/include/Eigen/src/Geometry/OrthoMethods.h \
		/usr/local/include/Eigen/src/Geometry/EulerAngles.h \
		/usr/local/include/Eigen/src/Geometry/Homogeneous.h \
		/usr/local/include/Eigen/src/Geometry/RotationBase.h \
		/usr/local/include/Eigen/src/Geometry/Rotation2D.h \
		/usr/local/include/Eigen/src/Geometry/Quaternion.h \
		/usr/local/include/Eigen/src/Geometry/AngleAxis.h \
		/usr/local/include/Eigen/src/Geometry/Transform.h \
		/usr/local/include/Eigen/src/Geometry/Translation.h \
		/usr/local/include/Eigen/src/Geometry/Scaling.h \
		/usr/local/include/Eigen/src/Geometry/Hyperplane.h \
		/usr/local/include/Eigen/src/Geometry/ParametrizedLine.h \
		/usr/local/include/Eigen/src/Geometry/AlignedBox.h \
		/usr/local/include/Eigen/src/Geometry/Umeyama.h \
		/usr/local/include/Eigen/src/Geometry/arch/Geometry_SIMD.h \
		/usr/local/include/Eigen/Eigenvalues \
		/usr/local/include/Eigen/src/Eigenvalues/Tridiagonalization.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/HessenbergDecomposition.h \
		/usr/local/include/Eigen/src/Eigenvalues/EigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealQZ.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/MatrixBaseEigenvalues.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver_LAPACKE.h \
		../../optimizertypes.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDataStream \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdatastream.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QByteArray \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qbytearray.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutex \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qmutex.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutexLocker \
		../../trainingworker.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QObject \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qobject.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QWaitCondition \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qwaitcondition.h \
		../../losscurvewidget.h \
		/opt/Qt/6.9.1/macos/libexec/moc
	/opt/Qt/6.9.1/macos/libexec/moc $(DEFINES) -D__APPLE__ -D__GNUC__=4 -D__APPLE_CC__ -D__cplusplus=201402L -D__APPLE_CC__=6000 -D__clang__ -D__clang_major__=17 -D__clang_minor__=0 -D__clang_patchlevel__=0 -D__GNUC__=4 -D__GNUC_MINOR__=2 -D__GNUC_PATCHLEVEL__=1 -I/opt/Qt/6.9.1/macos/mkspecs/macx-clang -I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser -I/usr/local/include/Eigen -I/opt/Qt/6.9.1/macos/lib/QtCharts.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtOpenGLWidgets.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtOpenGL.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers -I. -I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/opt/Qt/6.9.1/macos/lib ../../mainwindow.h -o moc_mainwindow.cpp

moc_trainingworker.cpp: ../../trainingworker.h \
		../../mlp.h \
		../../layer.h \
		/usr/local/include/Eigen/Dense \
		/usr/local/include/Eigen/Core \
		/usr/local/include/Eigen/src/Core/util/DisableStupidWarnings.h \
		/usr/local/include/Eigen/src/Core/util/Macros.h \
		/usr/local/include/Eigen/src/Core/util/ConfigureVectorization.h \
		/usr/local/include/Eigen/src/Core/util/MKL_support.h \
		/usr/local/include/Eigen/src/misc/blas.h \
		/usr/local/include/Eigen/src/Core/util/Constants.h \
		/usr/local/include/Eigen/src/Core/util/Meta.h \
		/usr/local/include/Eigen/src/Core/util/ForwardDeclarations.h \
		/usr/local/include/Eigen/src/Core/util/StaticAssert.h \
		/usr/local/include/Eigen/src/Core/util/XprHelper.h \
		/usr/local/include/Eigen/src/Core/util/Memory.h \
		/usr/local/include/Eigen/src/Core/util/IntegralConstant.h \
		/usr/local/include/Eigen/src/Core/util/SymbolicIndex.h \
		/usr/local/include/Eigen/src/Core/NumTraits.h \
		/usr/local/include/Eigen/src/Core/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/GenericPacketMath.h \
		/usr/local/include/Eigen/src/Core/MathFunctionsImpl.h \
		/usr/local/include/Eigen/src/Core/arch/Default/ConjHelper.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Half.h \
		/usr/local/include/Eigen/src/Core/arch/Default/BFloat16.h \
		/usr/local/include/Eigen/src/Core/arch/Default/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctionsFwd.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/SyclMemoryModel.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/InteropHeaders.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Settings.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctions.h \
		/usr/local/include/Eigen/src/Core/functors/TernaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/BinaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/UnaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/NullaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/StlFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/AssignmentFunctors.h \
		/usr/local/include/Eigen/src/Core/arch/CUDA/Complex.h \
		/usr/local/include/Eigen/src/Core/util/IndexedViewHelper.h \
		/usr/local/include/Eigen/src/Core/util/ReshapedHelper.h \
		/usr/local/include/Eigen/src/Core/ArithmeticSequence.h \
		/usr/local/include/Eigen/src/Core/IO.h \
		/usr/local/include/Eigen/src/Core/DenseCoeffsBase.h \
		/usr/local/include/Eigen/src/Core/DenseBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/BlockMethods.h \
		/usr/local/include/Eigen/src/plugins/IndexedViewMethods.h \
		/usr/local/include/Eigen/src/plugins/ReshapedMethods.h \
		/usr/local/include/Eigen/src/Core/MatrixBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/EigenBase.h \
		/usr/local/include/Eigen/src/Core/Product.h \
		/usr/local/include/Eigen/src/Core/CoreEvaluators.h \
		/usr/local/include/Eigen/src/Core/AssignEvaluator.h \
		/usr/local/include/Eigen/src/Core/Assign.h \
		/usr/local/include/Eigen/src/Core/ArrayBase.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/util/BlasUtil.h \
		/usr/local/include/Eigen/src/Core/DenseStorage.h \
		/usr/local/include/Eigen/src/Core/NestByValue.h \
		/usr/local/include/Eigen/src/Core/ReturnByValue.h \
		/usr/local/include/Eigen/src/Core/NoAlias.h \
		/usr/local/include/Eigen/src/Core/PlainObjectBase.h \
		/usr/local/include/Eigen/src/Core/Matrix.h \
		/usr/local/include/Eigen/src/Core/Array.h \
		/usr/local/include/Eigen/src/Core/CwiseTernaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseNullaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryView.h \
		/usr/local/include/Eigen/src/Core/SelfCwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/Dot.h \
		/usr/local/include/Eigen/src/Core/StableNorm.h \
		/usr/local/include/Eigen/src/Core/Stride.h \
		/usr/local/include/Eigen/src/Core/MapBase.h \
		/usr/local/include/Eigen/src/Core/Map.h \
		/usr/local/include/Eigen/src/Core/Ref.h \
		/usr/local/include/Eigen/src/Core/Block.h \
		/usr/local/include/Eigen/src/Core/VectorBlock.h \
		/usr/local/include/Eigen/src/Core/IndexedView.h \
		/usr/local/include/Eigen/src/Core/Reshaped.h \
		/usr/local/include/Eigen/src/Core/Transpose.h \
		/usr/local/include/Eigen/src/Core/DiagonalMatrix.h \
		/usr/local/include/Eigen/src/Core/Diagonal.h \
		/usr/local/include/Eigen/src/Core/DiagonalProduct.h \
		/usr/local/include/Eigen/src/Core/Redux.h \
		/usr/local/include/Eigen/src/Core/Visitor.h \
		/usr/local/include/Eigen/src/Core/Fuzzy.h \
		/usr/local/include/Eigen/src/Core/Swap.h \
		/usr/local/include/Eigen/src/Core/CommaInitializer.h \
		/usr/local/include/Eigen/src/Core/GeneralProduct.h \
		/usr/local/include/Eigen/src/Core/Solve.h \
		/usr/local/include/Eigen/src/Core/Inverse.h \
		/usr/local/include/Eigen/src/Core/SolverBase.h \
		/usr/local/include/Eigen/src/Core/PermutationMatrix.h \
		/usr/local/include/Eigen/src/Core/Transpositions.h \
		/usr/local/include/Eigen/src/Core/TriangularMatrix.h \
		/usr/local/include/Eigen/src/Core/SelfAdjointView.h \
		/usr/local/include/Eigen/src/Core/products/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/products/Parallelizer.h \
		/usr/local/include/Eigen/src/Core/ProductEvaluators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/SolveTriangular.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointProduct.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointRank2Update.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverVector.h \
		/usr/local/include/Eigen/src/Core/BandMatrix.h \
		/usr/local/include/Eigen/src/Core/CoreIterators.h \
		/usr/local/include/Eigen/src/Core/ConditionEstimator.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProduct.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductCommon.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductMMA.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/BooleanRedux.h \
		/usr/local/include/Eigen/src/Core/Select.h \
		/usr/local/include/Eigen/src/Core/VectorwiseOp.h \
		/usr/local/include/Eigen/src/Core/PartialReduxEvaluator.h \
		/usr/local/include/Eigen/src/Core/Random.h \
		/usr/local/include/Eigen/src/Core/Replicate.h \
		/usr/local/include/Eigen/src/Core/Reverse.h \
		/usr/local/include/Eigen/src/Core/ArrayWrapper.h \
		/usr/local/include/Eigen/src/Core/StlIterators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/Assign_MKL.h \
		/usr/local/include/Eigen/src/Core/GlobalFunctions.h \
		/usr/local/include/Eigen/src/Core/util/ReenableStupidWarnings.h \
		/usr/local/include/Eigen/LU \
		/usr/local/include/Eigen/src/misc/Kernel.h \
		/usr/local/include/Eigen/src/misc/Image.h \
		/usr/local/include/Eigen/src/LU/FullPivLU.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU.h \
		/usr/local/include/Eigen/src/misc/lapacke.h \
		/usr/local/include/Eigen/src/misc/lapacke_mangling.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU_LAPACKE.h \
		/usr/local/include/Eigen/src/LU/Determinant.h \
		/usr/local/include/Eigen/src/LU/InverseImpl.h \
		/usr/local/include/Eigen/src/LU/arch/InverseSize4.h \
		/usr/local/include/Eigen/Cholesky \
		/usr/local/include/Eigen/Jacobi \
		/usr/local/include/Eigen/src/Jacobi/Jacobi.h \
		/usr/local/include/Eigen/src/Cholesky/LLT.h \
		/usr/local/include/Eigen/src/Cholesky/LDLT.h \
		/usr/local/include/Eigen/src/Cholesky/LLT_LAPACKE.h \
		/usr/local/include/Eigen/QR \
		/usr/local/include/Eigen/Householder \
		/usr/local/include/Eigen/src/Householder/Householder.h \
		/usr/local/include/Eigen/src/Householder/HouseholderSequence.h \
		/usr/local/include/Eigen/src/Householder/BlockHouseholder.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR.h \
		/usr/local/include/Eigen/src/QR/FullPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/CompleteOrthogonalDecomposition.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/SVD \
		/usr/local/include/Eigen/src/misc/RealSvd2x2.h \
		/usr/local/include/Eigen/src/SVD/UpperBidiagonalization.h \
		/usr/local/include/Eigen/src/SVD/SVDBase.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD.h \
		/usr/local/include/Eigen/src/SVD/BDCSVD.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD_LAPACKE.h \
		/usr/local/include/Eigen/Geometry \
		/usr/local/include/Eigen/src/Geometry/OrthoMethods.h \
		/usr/local/include/Eigen/src/Geometry/EulerAngles.h \
		/usr/local/include/Eigen/src/Geometry/Homogeneous.h \
		/usr/local/include/Eigen/src/Geometry/RotationBase.h \
		/usr/local/include/Eigen/src/Geometry/Rotation2D.h \
		/usr/local/include/Eigen/src/Geometry/Quaternion.h \
		/usr/local/include/Eigen/src/Geometry/AngleAxis.h \
		/usr/local/include/Eigen/src/Geometry/Transform.h \
		/usr/local/include/Eigen/src/Geometry/Translation.h \
		/usr/local/include/Eigen/src/Geometry/Scaling.h \
		/usr/local/include/Eigen/src/Geometry/Hyperplane.h \
		/usr/local/include/Eigen/src/Geometry/ParametrizedLine.h \
		/usr/local/include/Eigen/src/Geometry/AlignedBox.h \
		/usr/local/include/Eigen/src/Geometry/Umeyama.h \
		/usr/local/include/Eigen/src/Geometry/arch/Geometry_SIMD.h \
		/usr/local/include/Eigen/Eigenvalues \
		/usr/local/include/Eigen/src/Eigenvalues/Tridiagonalization.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/HessenbergDecomposition.h \
		/usr/local/include/Eigen/src/Eigenvalues/EigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealQZ.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/MatrixBaseEigenvalues.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver_LAPACKE.h \
		../../optimizertypes.h \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/QImage \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/qimage.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonObject \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsonobject.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QFile \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qfile.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDataStream \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdatastream.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QByteArray \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qbytearray.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonDocument \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsondocument.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutex \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qmutex.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutexLocker \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QObject \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qobject.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QThread \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qthread.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QWaitCondition \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qwaitcondition.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDir \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdir.h \
		/opt/Qt/6.9.1/macos/libexec/moc
	/opt/Qt/6.9.1/macos/libexec/moc $(DEFINES) -D__APPLE__ -D__GNUC__=4 -D__APPLE_CC__ -D__cplusplus=201402L -D__APPLE_CC__=6000 -D__clang__ -D__clang_major__=17 -D__clang_minor__=0 -D__clang_patchlevel__=0 -D__GNUC__=4 -D__GNUC_MINOR__=2 -D__GNUC_PATCHLEVEL__=1 -I/opt/Qt/6.9.1/macos/mkspecs/macx-clang -I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser -I/usr/local/include/Eigen -I/opt/Qt/6.9.1/macos/lib/QtCharts.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtOpenGLWidgets.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtOpenGL.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers -I. -I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/opt/Qt/6.9.1/macos/lib ../../trainingworker.h -o moc_trainingworker.cpp

moc_losscurvewidget.cpp: ../../losscurvewidget.h \
		/opt/Qt/6.9.1/macos/libexec/moc
	/opt/Qt/6.9.1/macos/libexec/moc $(DEFINES) -D__APPLE__ -D__GNUC__=4 -D__APPLE_CC__ -D__cplusplus=201402L -D__APPLE_CC__=6000 -D__clang__ -D__clang_major__=17 -D__clang_minor__=0 -D__clang_patchlevel__=0 -D__GNUC__=4 -D__GNUC_MINOR__=2 -D__GNUC_PATCHLEVEL__=1 -I/opt/Qt/6.9.1/macos/mkspecs/macx-clang -I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser -I/usr/local/include/Eigen -I/opt/Qt/6.9.1/macos/lib/QtCharts.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtOpenGLWidgets.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtOpenGL.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers -I/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers -I. -I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 -I/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include -I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include -I/Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -F/opt/Qt/6.9.1/macos/lib ../../losscurvewidget.h -o moc_losscurvewidget.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h
ui_mainwindow.h: ../../mainwindow.ui \
		/opt/Qt/6.9.1/macos/libexec/uic
	/opt/Qt/6.9.1/macos/libexec/uic ../../mainwindow.ui -o ui_mainwindow.h

compiler_rez_source_make_all:
compiler_rez_source_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_lrelease_clean compiler_rcc_clean compiler_moc_header_clean compiler_uic_clean 

####### Compile

main.o: ../../main.cpp ../../mainwindow.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QMainWindow \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qmainwindow.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QThread \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qthread.h \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/QImage \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/qimage.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QFileDialog \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qfiledialog.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QMessageBox \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qmessagebox.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonDocument \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsondocument.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonObject \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsonobject.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QFile \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qfile.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDir \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdir.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QGraphicsScene \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qgraphicsscene.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QGraphicsPixmapItem \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qgraphicsitem.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QTimer \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qtimer.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QListWidget \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qlistwidget.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QSpinBox \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qspinbox.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QComboBox \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qcombobox.h \
		../../mlp.h \
		../../layer.h \
		/usr/local/include/Eigen/Dense \
		/usr/local/include/Eigen/Core \
		/usr/local/include/Eigen/src/Core/util/DisableStupidWarnings.h \
		/usr/local/include/Eigen/src/Core/util/Macros.h \
		/usr/local/include/Eigen/src/Core/util/ConfigureVectorization.h \
		/usr/local/include/Eigen/src/Core/util/MKL_support.h \
		/usr/local/include/Eigen/src/misc/blas.h \
		/usr/local/include/Eigen/src/Core/util/Constants.h \
		/usr/local/include/Eigen/src/Core/util/Meta.h \
		/usr/local/include/Eigen/src/Core/util/ForwardDeclarations.h \
		/usr/local/include/Eigen/src/Core/util/StaticAssert.h \
		/usr/local/include/Eigen/src/Core/util/XprHelper.h \
		/usr/local/include/Eigen/src/Core/util/Memory.h \
		/usr/local/include/Eigen/src/Core/util/IntegralConstant.h \
		/usr/local/include/Eigen/src/Core/util/SymbolicIndex.h \
		/usr/local/include/Eigen/src/Core/NumTraits.h \
		/usr/local/include/Eigen/src/Core/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/GenericPacketMath.h \
		/usr/local/include/Eigen/src/Core/MathFunctionsImpl.h \
		/usr/local/include/Eigen/src/Core/arch/Default/ConjHelper.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Half.h \
		/usr/local/include/Eigen/src/Core/arch/Default/BFloat16.h \
		/usr/local/include/Eigen/src/Core/arch/Default/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctionsFwd.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/SyclMemoryModel.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/InteropHeaders.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Settings.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctions.h \
		/usr/local/include/Eigen/src/Core/functors/TernaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/BinaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/UnaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/NullaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/StlFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/AssignmentFunctors.h \
		/usr/local/include/Eigen/src/Core/arch/CUDA/Complex.h \
		/usr/local/include/Eigen/src/Core/util/IndexedViewHelper.h \
		/usr/local/include/Eigen/src/Core/util/ReshapedHelper.h \
		/usr/local/include/Eigen/src/Core/ArithmeticSequence.h \
		/usr/local/include/Eigen/src/Core/IO.h \
		/usr/local/include/Eigen/src/Core/DenseCoeffsBase.h \
		/usr/local/include/Eigen/src/Core/DenseBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/BlockMethods.h \
		/usr/local/include/Eigen/src/plugins/IndexedViewMethods.h \
		/usr/local/include/Eigen/src/plugins/ReshapedMethods.h \
		/usr/local/include/Eigen/src/Core/MatrixBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/EigenBase.h \
		/usr/local/include/Eigen/src/Core/Product.h \
		/usr/local/include/Eigen/src/Core/CoreEvaluators.h \
		/usr/local/include/Eigen/src/Core/AssignEvaluator.h \
		/usr/local/include/Eigen/src/Core/Assign.h \
		/usr/local/include/Eigen/src/Core/ArrayBase.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/util/BlasUtil.h \
		/usr/local/include/Eigen/src/Core/DenseStorage.h \
		/usr/local/include/Eigen/src/Core/NestByValue.h \
		/usr/local/include/Eigen/src/Core/ReturnByValue.h \
		/usr/local/include/Eigen/src/Core/NoAlias.h \
		/usr/local/include/Eigen/src/Core/PlainObjectBase.h \
		/usr/local/include/Eigen/src/Core/Matrix.h \
		/usr/local/include/Eigen/src/Core/Array.h \
		/usr/local/include/Eigen/src/Core/CwiseTernaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseNullaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryView.h \
		/usr/local/include/Eigen/src/Core/SelfCwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/Dot.h \
		/usr/local/include/Eigen/src/Core/StableNorm.h \
		/usr/local/include/Eigen/src/Core/Stride.h \
		/usr/local/include/Eigen/src/Core/MapBase.h \
		/usr/local/include/Eigen/src/Core/Map.h \
		/usr/local/include/Eigen/src/Core/Ref.h \
		/usr/local/include/Eigen/src/Core/Block.h \
		/usr/local/include/Eigen/src/Core/VectorBlock.h \
		/usr/local/include/Eigen/src/Core/IndexedView.h \
		/usr/local/include/Eigen/src/Core/Reshaped.h \
		/usr/local/include/Eigen/src/Core/Transpose.h \
		/usr/local/include/Eigen/src/Core/DiagonalMatrix.h \
		/usr/local/include/Eigen/src/Core/Diagonal.h \
		/usr/local/include/Eigen/src/Core/DiagonalProduct.h \
		/usr/local/include/Eigen/src/Core/Redux.h \
		/usr/local/include/Eigen/src/Core/Visitor.h \
		/usr/local/include/Eigen/src/Core/Fuzzy.h \
		/usr/local/include/Eigen/src/Core/Swap.h \
		/usr/local/include/Eigen/src/Core/CommaInitializer.h \
		/usr/local/include/Eigen/src/Core/GeneralProduct.h \
		/usr/local/include/Eigen/src/Core/Solve.h \
		/usr/local/include/Eigen/src/Core/Inverse.h \
		/usr/local/include/Eigen/src/Core/SolverBase.h \
		/usr/local/include/Eigen/src/Core/PermutationMatrix.h \
		/usr/local/include/Eigen/src/Core/Transpositions.h \
		/usr/local/include/Eigen/src/Core/TriangularMatrix.h \
		/usr/local/include/Eigen/src/Core/SelfAdjointView.h \
		/usr/local/include/Eigen/src/Core/products/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/products/Parallelizer.h \
		/usr/local/include/Eigen/src/Core/ProductEvaluators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/SolveTriangular.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointProduct.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointRank2Update.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverVector.h \
		/usr/local/include/Eigen/src/Core/BandMatrix.h \
		/usr/local/include/Eigen/src/Core/CoreIterators.h \
		/usr/local/include/Eigen/src/Core/ConditionEstimator.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProduct.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductCommon.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductMMA.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/BooleanRedux.h \
		/usr/local/include/Eigen/src/Core/Select.h \
		/usr/local/include/Eigen/src/Core/VectorwiseOp.h \
		/usr/local/include/Eigen/src/Core/PartialReduxEvaluator.h \
		/usr/local/include/Eigen/src/Core/Random.h \
		/usr/local/include/Eigen/src/Core/Replicate.h \
		/usr/local/include/Eigen/src/Core/Reverse.h \
		/usr/local/include/Eigen/src/Core/ArrayWrapper.h \
		/usr/local/include/Eigen/src/Core/StlIterators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/Assign_MKL.h \
		/usr/local/include/Eigen/src/Core/GlobalFunctions.h \
		/usr/local/include/Eigen/src/Core/util/ReenableStupidWarnings.h \
		/usr/local/include/Eigen/LU \
		/usr/local/include/Eigen/src/misc/Kernel.h \
		/usr/local/include/Eigen/src/misc/Image.h \
		/usr/local/include/Eigen/src/LU/FullPivLU.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU.h \
		/usr/local/include/Eigen/src/misc/lapacke.h \
		/usr/local/include/Eigen/src/misc/lapacke_mangling.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU_LAPACKE.h \
		/usr/local/include/Eigen/src/LU/Determinant.h \
		/usr/local/include/Eigen/src/LU/InverseImpl.h \
		/usr/local/include/Eigen/src/LU/arch/InverseSize4.h \
		/usr/local/include/Eigen/Cholesky \
		/usr/local/include/Eigen/Jacobi \
		/usr/local/include/Eigen/src/Jacobi/Jacobi.h \
		/usr/local/include/Eigen/src/Cholesky/LLT.h \
		/usr/local/include/Eigen/src/Cholesky/LDLT.h \
		/usr/local/include/Eigen/src/Cholesky/LLT_LAPACKE.h \
		/usr/local/include/Eigen/QR \
		/usr/local/include/Eigen/Householder \
		/usr/local/include/Eigen/src/Householder/Householder.h \
		/usr/local/include/Eigen/src/Householder/HouseholderSequence.h \
		/usr/local/include/Eigen/src/Householder/BlockHouseholder.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR.h \
		/usr/local/include/Eigen/src/QR/FullPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/CompleteOrthogonalDecomposition.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/SVD \
		/usr/local/include/Eigen/src/misc/RealSvd2x2.h \
		/usr/local/include/Eigen/src/SVD/UpperBidiagonalization.h \
		/usr/local/include/Eigen/src/SVD/SVDBase.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD.h \
		/usr/local/include/Eigen/src/SVD/BDCSVD.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD_LAPACKE.h \
		/usr/local/include/Eigen/Geometry \
		/usr/local/include/Eigen/src/Geometry/OrthoMethods.h \
		/usr/local/include/Eigen/src/Geometry/EulerAngles.h \
		/usr/local/include/Eigen/src/Geometry/Homogeneous.h \
		/usr/local/include/Eigen/src/Geometry/RotationBase.h \
		/usr/local/include/Eigen/src/Geometry/Rotation2D.h \
		/usr/local/include/Eigen/src/Geometry/Quaternion.h \
		/usr/local/include/Eigen/src/Geometry/AngleAxis.h \
		/usr/local/include/Eigen/src/Geometry/Transform.h \
		/usr/local/include/Eigen/src/Geometry/Translation.h \
		/usr/local/include/Eigen/src/Geometry/Scaling.h \
		/usr/local/include/Eigen/src/Geometry/Hyperplane.h \
		/usr/local/include/Eigen/src/Geometry/ParametrizedLine.h \
		/usr/local/include/Eigen/src/Geometry/AlignedBox.h \
		/usr/local/include/Eigen/src/Geometry/Umeyama.h \
		/usr/local/include/Eigen/src/Geometry/arch/Geometry_SIMD.h \
		/usr/local/include/Eigen/Eigenvalues \
		/usr/local/include/Eigen/src/Eigenvalues/Tridiagonalization.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/HessenbergDecomposition.h \
		/usr/local/include/Eigen/src/Eigenvalues/EigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealQZ.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/MatrixBaseEigenvalues.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver_LAPACKE.h \
		../../optimizertypes.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDataStream \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdatastream.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QByteArray \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qbytearray.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutex \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qmutex.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutexLocker \
		../../trainingworker.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QObject \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qobject.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QWaitCondition \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qwaitcondition.h \
		../../losscurvewidget.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QApplication \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qapplication.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QLocale \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qlocale.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QTranslator \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qtranslator.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o ../../main.cpp

mainwindow.o: ../../mainwindow.cpp ../../mainwindow.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QMainWindow \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qmainwindow.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QThread \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qthread.h \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/QImage \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/qimage.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QFileDialog \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qfiledialog.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QMessageBox \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qmessagebox.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonDocument \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsondocument.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonObject \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsonobject.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QFile \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qfile.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDir \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdir.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QGraphicsScene \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qgraphicsscene.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QGraphicsPixmapItem \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qgraphicsitem.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QTimer \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qtimer.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QListWidget \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qlistwidget.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QSpinBox \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qspinbox.h \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/QComboBox \
		/opt/Qt/6.9.1/macos/lib/QtWidgets.framework/Headers/qcombobox.h \
		../../mlp.h \
		../../layer.h \
		/usr/local/include/Eigen/Dense \
		/usr/local/include/Eigen/Core \
		/usr/local/include/Eigen/src/Core/util/DisableStupidWarnings.h \
		/usr/local/include/Eigen/src/Core/util/Macros.h \
		/usr/local/include/Eigen/src/Core/util/ConfigureVectorization.h \
		/usr/local/include/Eigen/src/Core/util/MKL_support.h \
		/usr/local/include/Eigen/src/misc/blas.h \
		/usr/local/include/Eigen/src/Core/util/Constants.h \
		/usr/local/include/Eigen/src/Core/util/Meta.h \
		/usr/local/include/Eigen/src/Core/util/ForwardDeclarations.h \
		/usr/local/include/Eigen/src/Core/util/StaticAssert.h \
		/usr/local/include/Eigen/src/Core/util/XprHelper.h \
		/usr/local/include/Eigen/src/Core/util/Memory.h \
		/usr/local/include/Eigen/src/Core/util/IntegralConstant.h \
		/usr/local/include/Eigen/src/Core/util/SymbolicIndex.h \
		/usr/local/include/Eigen/src/Core/NumTraits.h \
		/usr/local/include/Eigen/src/Core/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/GenericPacketMath.h \
		/usr/local/include/Eigen/src/Core/MathFunctionsImpl.h \
		/usr/local/include/Eigen/src/Core/arch/Default/ConjHelper.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Half.h \
		/usr/local/include/Eigen/src/Core/arch/Default/BFloat16.h \
		/usr/local/include/Eigen/src/Core/arch/Default/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctionsFwd.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/SyclMemoryModel.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/InteropHeaders.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Settings.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctions.h \
		/usr/local/include/Eigen/src/Core/functors/TernaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/BinaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/UnaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/NullaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/StlFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/AssignmentFunctors.h \
		/usr/local/include/Eigen/src/Core/arch/CUDA/Complex.h \
		/usr/local/include/Eigen/src/Core/util/IndexedViewHelper.h \
		/usr/local/include/Eigen/src/Core/util/ReshapedHelper.h \
		/usr/local/include/Eigen/src/Core/ArithmeticSequence.h \
		/usr/local/include/Eigen/src/Core/IO.h \
		/usr/local/include/Eigen/src/Core/DenseCoeffsBase.h \
		/usr/local/include/Eigen/src/Core/DenseBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/BlockMethods.h \
		/usr/local/include/Eigen/src/plugins/IndexedViewMethods.h \
		/usr/local/include/Eigen/src/plugins/ReshapedMethods.h \
		/usr/local/include/Eigen/src/Core/MatrixBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/EigenBase.h \
		/usr/local/include/Eigen/src/Core/Product.h \
		/usr/local/include/Eigen/src/Core/CoreEvaluators.h \
		/usr/local/include/Eigen/src/Core/AssignEvaluator.h \
		/usr/local/include/Eigen/src/Core/Assign.h \
		/usr/local/include/Eigen/src/Core/ArrayBase.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/util/BlasUtil.h \
		/usr/local/include/Eigen/src/Core/DenseStorage.h \
		/usr/local/include/Eigen/src/Core/NestByValue.h \
		/usr/local/include/Eigen/src/Core/ReturnByValue.h \
		/usr/local/include/Eigen/src/Core/NoAlias.h \
		/usr/local/include/Eigen/src/Core/PlainObjectBase.h \
		/usr/local/include/Eigen/src/Core/Matrix.h \
		/usr/local/include/Eigen/src/Core/Array.h \
		/usr/local/include/Eigen/src/Core/CwiseTernaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseNullaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryView.h \
		/usr/local/include/Eigen/src/Core/SelfCwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/Dot.h \
		/usr/local/include/Eigen/src/Core/StableNorm.h \
		/usr/local/include/Eigen/src/Core/Stride.h \
		/usr/local/include/Eigen/src/Core/MapBase.h \
		/usr/local/include/Eigen/src/Core/Map.h \
		/usr/local/include/Eigen/src/Core/Ref.h \
		/usr/local/include/Eigen/src/Core/Block.h \
		/usr/local/include/Eigen/src/Core/VectorBlock.h \
		/usr/local/include/Eigen/src/Core/IndexedView.h \
		/usr/local/include/Eigen/src/Core/Reshaped.h \
		/usr/local/include/Eigen/src/Core/Transpose.h \
		/usr/local/include/Eigen/src/Core/DiagonalMatrix.h \
		/usr/local/include/Eigen/src/Core/Diagonal.h \
		/usr/local/include/Eigen/src/Core/DiagonalProduct.h \
		/usr/local/include/Eigen/src/Core/Redux.h \
		/usr/local/include/Eigen/src/Core/Visitor.h \
		/usr/local/include/Eigen/src/Core/Fuzzy.h \
		/usr/local/include/Eigen/src/Core/Swap.h \
		/usr/local/include/Eigen/src/Core/CommaInitializer.h \
		/usr/local/include/Eigen/src/Core/GeneralProduct.h \
		/usr/local/include/Eigen/src/Core/Solve.h \
		/usr/local/include/Eigen/src/Core/Inverse.h \
		/usr/local/include/Eigen/src/Core/SolverBase.h \
		/usr/local/include/Eigen/src/Core/PermutationMatrix.h \
		/usr/local/include/Eigen/src/Core/Transpositions.h \
		/usr/local/include/Eigen/src/Core/TriangularMatrix.h \
		/usr/local/include/Eigen/src/Core/SelfAdjointView.h \
		/usr/local/include/Eigen/src/Core/products/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/products/Parallelizer.h \
		/usr/local/include/Eigen/src/Core/ProductEvaluators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/SolveTriangular.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointProduct.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointRank2Update.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverVector.h \
		/usr/local/include/Eigen/src/Core/BandMatrix.h \
		/usr/local/include/Eigen/src/Core/CoreIterators.h \
		/usr/local/include/Eigen/src/Core/ConditionEstimator.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProduct.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductCommon.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductMMA.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/BooleanRedux.h \
		/usr/local/include/Eigen/src/Core/Select.h \
		/usr/local/include/Eigen/src/Core/VectorwiseOp.h \
		/usr/local/include/Eigen/src/Core/PartialReduxEvaluator.h \
		/usr/local/include/Eigen/src/Core/Random.h \
		/usr/local/include/Eigen/src/Core/Replicate.h \
		/usr/local/include/Eigen/src/Core/Reverse.h \
		/usr/local/include/Eigen/src/Core/ArrayWrapper.h \
		/usr/local/include/Eigen/src/Core/StlIterators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/Assign_MKL.h \
		/usr/local/include/Eigen/src/Core/GlobalFunctions.h \
		/usr/local/include/Eigen/src/Core/util/ReenableStupidWarnings.h \
		/usr/local/include/Eigen/LU \
		/usr/local/include/Eigen/src/misc/Kernel.h \
		/usr/local/include/Eigen/src/misc/Image.h \
		/usr/local/include/Eigen/src/LU/FullPivLU.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU.h \
		/usr/local/include/Eigen/src/misc/lapacke.h \
		/usr/local/include/Eigen/src/misc/lapacke_mangling.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU_LAPACKE.h \
		/usr/local/include/Eigen/src/LU/Determinant.h \
		/usr/local/include/Eigen/src/LU/InverseImpl.h \
		/usr/local/include/Eigen/src/LU/arch/InverseSize4.h \
		/usr/local/include/Eigen/Cholesky \
		/usr/local/include/Eigen/Jacobi \
		/usr/local/include/Eigen/src/Jacobi/Jacobi.h \
		/usr/local/include/Eigen/src/Cholesky/LLT.h \
		/usr/local/include/Eigen/src/Cholesky/LDLT.h \
		/usr/local/include/Eigen/src/Cholesky/LLT_LAPACKE.h \
		/usr/local/include/Eigen/QR \
		/usr/local/include/Eigen/Householder \
		/usr/local/include/Eigen/src/Householder/Householder.h \
		/usr/local/include/Eigen/src/Householder/HouseholderSequence.h \
		/usr/local/include/Eigen/src/Householder/BlockHouseholder.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR.h \
		/usr/local/include/Eigen/src/QR/FullPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/CompleteOrthogonalDecomposition.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/SVD \
		/usr/local/include/Eigen/src/misc/RealSvd2x2.h \
		/usr/local/include/Eigen/src/SVD/UpperBidiagonalization.h \
		/usr/local/include/Eigen/src/SVD/SVDBase.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD.h \
		/usr/local/include/Eigen/src/SVD/BDCSVD.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD_LAPACKE.h \
		/usr/local/include/Eigen/Geometry \
		/usr/local/include/Eigen/src/Geometry/OrthoMethods.h \
		/usr/local/include/Eigen/src/Geometry/EulerAngles.h \
		/usr/local/include/Eigen/src/Geometry/Homogeneous.h \
		/usr/local/include/Eigen/src/Geometry/RotationBase.h \
		/usr/local/include/Eigen/src/Geometry/Rotation2D.h \
		/usr/local/include/Eigen/src/Geometry/Quaternion.h \
		/usr/local/include/Eigen/src/Geometry/AngleAxis.h \
		/usr/local/include/Eigen/src/Geometry/Transform.h \
		/usr/local/include/Eigen/src/Geometry/Translation.h \
		/usr/local/include/Eigen/src/Geometry/Scaling.h \
		/usr/local/include/Eigen/src/Geometry/Hyperplane.h \
		/usr/local/include/Eigen/src/Geometry/ParametrizedLine.h \
		/usr/local/include/Eigen/src/Geometry/AlignedBox.h \
		/usr/local/include/Eigen/src/Geometry/Umeyama.h \
		/usr/local/include/Eigen/src/Geometry/arch/Geometry_SIMD.h \
		/usr/local/include/Eigen/Eigenvalues \
		/usr/local/include/Eigen/src/Eigenvalues/Tridiagonalization.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/HessenbergDecomposition.h \
		/usr/local/include/Eigen/src/Eigenvalues/EigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealQZ.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/MatrixBaseEigenvalues.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver_LAPACKE.h \
		../../optimizertypes.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDataStream \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdatastream.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QByteArray \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qbytearray.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutex \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qmutex.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutexLocker \
		../../trainingworker.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QObject \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qobject.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QWaitCondition \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qwaitcondition.h \
		../../losscurvewidget.h \
		../../ui_mainwindow.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDebug \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdebug.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDirIterator \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdiriterator.h \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/QImageReader \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/qimagereader.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QFileInfo \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qfileinfo.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDateTime \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdatetime.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QStandardPaths \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qstandardpaths.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o mainwindow.o ../../mainwindow.cpp

mlp.o: ../../mlp.cpp ../../mlp.h \
		../../layer.h \
		/usr/local/include/Eigen/Dense \
		/usr/local/include/Eigen/Core \
		/usr/local/include/Eigen/src/Core/util/DisableStupidWarnings.h \
		/usr/local/include/Eigen/src/Core/util/Macros.h \
		/usr/local/include/Eigen/src/Core/util/ConfigureVectorization.h \
		/usr/local/include/Eigen/src/Core/util/MKL_support.h \
		/usr/local/include/Eigen/src/misc/blas.h \
		/usr/local/include/Eigen/src/Core/util/Constants.h \
		/usr/local/include/Eigen/src/Core/util/Meta.h \
		/usr/local/include/Eigen/src/Core/util/ForwardDeclarations.h \
		/usr/local/include/Eigen/src/Core/util/StaticAssert.h \
		/usr/local/include/Eigen/src/Core/util/XprHelper.h \
		/usr/local/include/Eigen/src/Core/util/Memory.h \
		/usr/local/include/Eigen/src/Core/util/IntegralConstant.h \
		/usr/local/include/Eigen/src/Core/util/SymbolicIndex.h \
		/usr/local/include/Eigen/src/Core/NumTraits.h \
		/usr/local/include/Eigen/src/Core/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/GenericPacketMath.h \
		/usr/local/include/Eigen/src/Core/MathFunctionsImpl.h \
		/usr/local/include/Eigen/src/Core/arch/Default/ConjHelper.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Half.h \
		/usr/local/include/Eigen/src/Core/arch/Default/BFloat16.h \
		/usr/local/include/Eigen/src/Core/arch/Default/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctionsFwd.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/SyclMemoryModel.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/InteropHeaders.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Settings.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctions.h \
		/usr/local/include/Eigen/src/Core/functors/TernaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/BinaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/UnaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/NullaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/StlFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/AssignmentFunctors.h \
		/usr/local/include/Eigen/src/Core/arch/CUDA/Complex.h \
		/usr/local/include/Eigen/src/Core/util/IndexedViewHelper.h \
		/usr/local/include/Eigen/src/Core/util/ReshapedHelper.h \
		/usr/local/include/Eigen/src/Core/ArithmeticSequence.h \
		/usr/local/include/Eigen/src/Core/IO.h \
		/usr/local/include/Eigen/src/Core/DenseCoeffsBase.h \
		/usr/local/include/Eigen/src/Core/DenseBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/BlockMethods.h \
		/usr/local/include/Eigen/src/plugins/IndexedViewMethods.h \
		/usr/local/include/Eigen/src/plugins/ReshapedMethods.h \
		/usr/local/include/Eigen/src/Core/MatrixBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/EigenBase.h \
		/usr/local/include/Eigen/src/Core/Product.h \
		/usr/local/include/Eigen/src/Core/CoreEvaluators.h \
		/usr/local/include/Eigen/src/Core/AssignEvaluator.h \
		/usr/local/include/Eigen/src/Core/Assign.h \
		/usr/local/include/Eigen/src/Core/ArrayBase.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/util/BlasUtil.h \
		/usr/local/include/Eigen/src/Core/DenseStorage.h \
		/usr/local/include/Eigen/src/Core/NestByValue.h \
		/usr/local/include/Eigen/src/Core/ReturnByValue.h \
		/usr/local/include/Eigen/src/Core/NoAlias.h \
		/usr/local/include/Eigen/src/Core/PlainObjectBase.h \
		/usr/local/include/Eigen/src/Core/Matrix.h \
		/usr/local/include/Eigen/src/Core/Array.h \
		/usr/local/include/Eigen/src/Core/CwiseTernaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseNullaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryView.h \
		/usr/local/include/Eigen/src/Core/SelfCwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/Dot.h \
		/usr/local/include/Eigen/src/Core/StableNorm.h \
		/usr/local/include/Eigen/src/Core/Stride.h \
		/usr/local/include/Eigen/src/Core/MapBase.h \
		/usr/local/include/Eigen/src/Core/Map.h \
		/usr/local/include/Eigen/src/Core/Ref.h \
		/usr/local/include/Eigen/src/Core/Block.h \
		/usr/local/include/Eigen/src/Core/VectorBlock.h \
		/usr/local/include/Eigen/src/Core/IndexedView.h \
		/usr/local/include/Eigen/src/Core/Reshaped.h \
		/usr/local/include/Eigen/src/Core/Transpose.h \
		/usr/local/include/Eigen/src/Core/DiagonalMatrix.h \
		/usr/local/include/Eigen/src/Core/Diagonal.h \
		/usr/local/include/Eigen/src/Core/DiagonalProduct.h \
		/usr/local/include/Eigen/src/Core/Redux.h \
		/usr/local/include/Eigen/src/Core/Visitor.h \
		/usr/local/include/Eigen/src/Core/Fuzzy.h \
		/usr/local/include/Eigen/src/Core/Swap.h \
		/usr/local/include/Eigen/src/Core/CommaInitializer.h \
		/usr/local/include/Eigen/src/Core/GeneralProduct.h \
		/usr/local/include/Eigen/src/Core/Solve.h \
		/usr/local/include/Eigen/src/Core/Inverse.h \
		/usr/local/include/Eigen/src/Core/SolverBase.h \
		/usr/local/include/Eigen/src/Core/PermutationMatrix.h \
		/usr/local/include/Eigen/src/Core/Transpositions.h \
		/usr/local/include/Eigen/src/Core/TriangularMatrix.h \
		/usr/local/include/Eigen/src/Core/SelfAdjointView.h \
		/usr/local/include/Eigen/src/Core/products/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/products/Parallelizer.h \
		/usr/local/include/Eigen/src/Core/ProductEvaluators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/SolveTriangular.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointProduct.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointRank2Update.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverVector.h \
		/usr/local/include/Eigen/src/Core/BandMatrix.h \
		/usr/local/include/Eigen/src/Core/CoreIterators.h \
		/usr/local/include/Eigen/src/Core/ConditionEstimator.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProduct.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductCommon.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductMMA.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/BooleanRedux.h \
		/usr/local/include/Eigen/src/Core/Select.h \
		/usr/local/include/Eigen/src/Core/VectorwiseOp.h \
		/usr/local/include/Eigen/src/Core/PartialReduxEvaluator.h \
		/usr/local/include/Eigen/src/Core/Random.h \
		/usr/local/include/Eigen/src/Core/Replicate.h \
		/usr/local/include/Eigen/src/Core/Reverse.h \
		/usr/local/include/Eigen/src/Core/ArrayWrapper.h \
		/usr/local/include/Eigen/src/Core/StlIterators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/Assign_MKL.h \
		/usr/local/include/Eigen/src/Core/GlobalFunctions.h \
		/usr/local/include/Eigen/src/Core/util/ReenableStupidWarnings.h \
		/usr/local/include/Eigen/LU \
		/usr/local/include/Eigen/src/misc/Kernel.h \
		/usr/local/include/Eigen/src/misc/Image.h \
		/usr/local/include/Eigen/src/LU/FullPivLU.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU.h \
		/usr/local/include/Eigen/src/misc/lapacke.h \
		/usr/local/include/Eigen/src/misc/lapacke_mangling.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU_LAPACKE.h \
		/usr/local/include/Eigen/src/LU/Determinant.h \
		/usr/local/include/Eigen/src/LU/InverseImpl.h \
		/usr/local/include/Eigen/src/LU/arch/InverseSize4.h \
		/usr/local/include/Eigen/Cholesky \
		/usr/local/include/Eigen/Jacobi \
		/usr/local/include/Eigen/src/Jacobi/Jacobi.h \
		/usr/local/include/Eigen/src/Cholesky/LLT.h \
		/usr/local/include/Eigen/src/Cholesky/LDLT.h \
		/usr/local/include/Eigen/src/Cholesky/LLT_LAPACKE.h \
		/usr/local/include/Eigen/QR \
		/usr/local/include/Eigen/Householder \
		/usr/local/include/Eigen/src/Householder/Householder.h \
		/usr/local/include/Eigen/src/Householder/HouseholderSequence.h \
		/usr/local/include/Eigen/src/Householder/BlockHouseholder.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR.h \
		/usr/local/include/Eigen/src/QR/FullPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/CompleteOrthogonalDecomposition.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/SVD \
		/usr/local/include/Eigen/src/misc/RealSvd2x2.h \
		/usr/local/include/Eigen/src/SVD/UpperBidiagonalization.h \
		/usr/local/include/Eigen/src/SVD/SVDBase.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD.h \
		/usr/local/include/Eigen/src/SVD/BDCSVD.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD_LAPACKE.h \
		/usr/local/include/Eigen/Geometry \
		/usr/local/include/Eigen/src/Geometry/OrthoMethods.h \
		/usr/local/include/Eigen/src/Geometry/EulerAngles.h \
		/usr/local/include/Eigen/src/Geometry/Homogeneous.h \
		/usr/local/include/Eigen/src/Geometry/RotationBase.h \
		/usr/local/include/Eigen/src/Geometry/Rotation2D.h \
		/usr/local/include/Eigen/src/Geometry/Quaternion.h \
		/usr/local/include/Eigen/src/Geometry/AngleAxis.h \
		/usr/local/include/Eigen/src/Geometry/Transform.h \
		/usr/local/include/Eigen/src/Geometry/Translation.h \
		/usr/local/include/Eigen/src/Geometry/Scaling.h \
		/usr/local/include/Eigen/src/Geometry/Hyperplane.h \
		/usr/local/include/Eigen/src/Geometry/ParametrizedLine.h \
		/usr/local/include/Eigen/src/Geometry/AlignedBox.h \
		/usr/local/include/Eigen/src/Geometry/Umeyama.h \
		/usr/local/include/Eigen/src/Geometry/arch/Geometry_SIMD.h \
		/usr/local/include/Eigen/Eigenvalues \
		/usr/local/include/Eigen/src/Eigenvalues/Tridiagonalization.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/HessenbergDecomposition.h \
		/usr/local/include/Eigen/src/Eigenvalues/EigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealQZ.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/MatrixBaseEigenvalues.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver_LAPACKE.h \
		../../optimizertypes.h \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/QImage \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/qimage.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonObject \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsonobject.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QFile \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qfile.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDataStream \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdatastream.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QByteArray \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qbytearray.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonDocument \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsondocument.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutex \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qmutex.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutexLocker \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonArray \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsonarray.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o mlp.o ../../mlp.cpp

layer.o: ../../layer.cpp ../../layer.h \
		/usr/local/include/Eigen/Dense \
		/usr/local/include/Eigen/Core \
		/usr/local/include/Eigen/src/Core/util/DisableStupidWarnings.h \
		/usr/local/include/Eigen/src/Core/util/Macros.h \
		/usr/local/include/Eigen/src/Core/util/ConfigureVectorization.h \
		/usr/local/include/Eigen/src/Core/util/MKL_support.h \
		/usr/local/include/Eigen/src/misc/blas.h \
		/usr/local/include/Eigen/src/Core/util/Constants.h \
		/usr/local/include/Eigen/src/Core/util/Meta.h \
		/usr/local/include/Eigen/src/Core/util/ForwardDeclarations.h \
		/usr/local/include/Eigen/src/Core/util/StaticAssert.h \
		/usr/local/include/Eigen/src/Core/util/XprHelper.h \
		/usr/local/include/Eigen/src/Core/util/Memory.h \
		/usr/local/include/Eigen/src/Core/util/IntegralConstant.h \
		/usr/local/include/Eigen/src/Core/util/SymbolicIndex.h \
		/usr/local/include/Eigen/src/Core/NumTraits.h \
		/usr/local/include/Eigen/src/Core/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/GenericPacketMath.h \
		/usr/local/include/Eigen/src/Core/MathFunctionsImpl.h \
		/usr/local/include/Eigen/src/Core/arch/Default/ConjHelper.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Half.h \
		/usr/local/include/Eigen/src/Core/arch/Default/BFloat16.h \
		/usr/local/include/Eigen/src/Core/arch/Default/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctionsFwd.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/SyclMemoryModel.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/InteropHeaders.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Settings.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctions.h \
		/usr/local/include/Eigen/src/Core/functors/TernaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/BinaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/UnaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/NullaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/StlFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/AssignmentFunctors.h \
		/usr/local/include/Eigen/src/Core/arch/CUDA/Complex.h \
		/usr/local/include/Eigen/src/Core/util/IndexedViewHelper.h \
		/usr/local/include/Eigen/src/Core/util/ReshapedHelper.h \
		/usr/local/include/Eigen/src/Core/ArithmeticSequence.h \
		/usr/local/include/Eigen/src/Core/IO.h \
		/usr/local/include/Eigen/src/Core/DenseCoeffsBase.h \
		/usr/local/include/Eigen/src/Core/DenseBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/BlockMethods.h \
		/usr/local/include/Eigen/src/plugins/IndexedViewMethods.h \
		/usr/local/include/Eigen/src/plugins/ReshapedMethods.h \
		/usr/local/include/Eigen/src/Core/MatrixBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/EigenBase.h \
		/usr/local/include/Eigen/src/Core/Product.h \
		/usr/local/include/Eigen/src/Core/CoreEvaluators.h \
		/usr/local/include/Eigen/src/Core/AssignEvaluator.h \
		/usr/local/include/Eigen/src/Core/Assign.h \
		/usr/local/include/Eigen/src/Core/ArrayBase.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/util/BlasUtil.h \
		/usr/local/include/Eigen/src/Core/DenseStorage.h \
		/usr/local/include/Eigen/src/Core/NestByValue.h \
		/usr/local/include/Eigen/src/Core/ReturnByValue.h \
		/usr/local/include/Eigen/src/Core/NoAlias.h \
		/usr/local/include/Eigen/src/Core/PlainObjectBase.h \
		/usr/local/include/Eigen/src/Core/Matrix.h \
		/usr/local/include/Eigen/src/Core/Array.h \
		/usr/local/include/Eigen/src/Core/CwiseTernaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseNullaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryView.h \
		/usr/local/include/Eigen/src/Core/SelfCwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/Dot.h \
		/usr/local/include/Eigen/src/Core/StableNorm.h \
		/usr/local/include/Eigen/src/Core/Stride.h \
		/usr/local/include/Eigen/src/Core/MapBase.h \
		/usr/local/include/Eigen/src/Core/Map.h \
		/usr/local/include/Eigen/src/Core/Ref.h \
		/usr/local/include/Eigen/src/Core/Block.h \
		/usr/local/include/Eigen/src/Core/VectorBlock.h \
		/usr/local/include/Eigen/src/Core/IndexedView.h \
		/usr/local/include/Eigen/src/Core/Reshaped.h \
		/usr/local/include/Eigen/src/Core/Transpose.h \
		/usr/local/include/Eigen/src/Core/DiagonalMatrix.h \
		/usr/local/include/Eigen/src/Core/Diagonal.h \
		/usr/local/include/Eigen/src/Core/DiagonalProduct.h \
		/usr/local/include/Eigen/src/Core/Redux.h \
		/usr/local/include/Eigen/src/Core/Visitor.h \
		/usr/local/include/Eigen/src/Core/Fuzzy.h \
		/usr/local/include/Eigen/src/Core/Swap.h \
		/usr/local/include/Eigen/src/Core/CommaInitializer.h \
		/usr/local/include/Eigen/src/Core/GeneralProduct.h \
		/usr/local/include/Eigen/src/Core/Solve.h \
		/usr/local/include/Eigen/src/Core/Inverse.h \
		/usr/local/include/Eigen/src/Core/SolverBase.h \
		/usr/local/include/Eigen/src/Core/PermutationMatrix.h \
		/usr/local/include/Eigen/src/Core/Transpositions.h \
		/usr/local/include/Eigen/src/Core/TriangularMatrix.h \
		/usr/local/include/Eigen/src/Core/SelfAdjointView.h \
		/usr/local/include/Eigen/src/Core/products/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/products/Parallelizer.h \
		/usr/local/include/Eigen/src/Core/ProductEvaluators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/SolveTriangular.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointProduct.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointRank2Update.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverVector.h \
		/usr/local/include/Eigen/src/Core/BandMatrix.h \
		/usr/local/include/Eigen/src/Core/CoreIterators.h \
		/usr/local/include/Eigen/src/Core/ConditionEstimator.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProduct.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductCommon.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductMMA.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/BooleanRedux.h \
		/usr/local/include/Eigen/src/Core/Select.h \
		/usr/local/include/Eigen/src/Core/VectorwiseOp.h \
		/usr/local/include/Eigen/src/Core/PartialReduxEvaluator.h \
		/usr/local/include/Eigen/src/Core/Random.h \
		/usr/local/include/Eigen/src/Core/Replicate.h \
		/usr/local/include/Eigen/src/Core/Reverse.h \
		/usr/local/include/Eigen/src/Core/ArrayWrapper.h \
		/usr/local/include/Eigen/src/Core/StlIterators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/Assign_MKL.h \
		/usr/local/include/Eigen/src/Core/GlobalFunctions.h \
		/usr/local/include/Eigen/src/Core/util/ReenableStupidWarnings.h \
		/usr/local/include/Eigen/LU \
		/usr/local/include/Eigen/src/misc/Kernel.h \
		/usr/local/include/Eigen/src/misc/Image.h \
		/usr/local/include/Eigen/src/LU/FullPivLU.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU.h \
		/usr/local/include/Eigen/src/misc/lapacke.h \
		/usr/local/include/Eigen/src/misc/lapacke_mangling.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU_LAPACKE.h \
		/usr/local/include/Eigen/src/LU/Determinant.h \
		/usr/local/include/Eigen/src/LU/InverseImpl.h \
		/usr/local/include/Eigen/src/LU/arch/InverseSize4.h \
		/usr/local/include/Eigen/Cholesky \
		/usr/local/include/Eigen/Jacobi \
		/usr/local/include/Eigen/src/Jacobi/Jacobi.h \
		/usr/local/include/Eigen/src/Cholesky/LLT.h \
		/usr/local/include/Eigen/src/Cholesky/LDLT.h \
		/usr/local/include/Eigen/src/Cholesky/LLT_LAPACKE.h \
		/usr/local/include/Eigen/QR \
		/usr/local/include/Eigen/Householder \
		/usr/local/include/Eigen/src/Householder/Householder.h \
		/usr/local/include/Eigen/src/Householder/HouseholderSequence.h \
		/usr/local/include/Eigen/src/Householder/BlockHouseholder.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR.h \
		/usr/local/include/Eigen/src/QR/FullPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/CompleteOrthogonalDecomposition.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/SVD \
		/usr/local/include/Eigen/src/misc/RealSvd2x2.h \
		/usr/local/include/Eigen/src/SVD/UpperBidiagonalization.h \
		/usr/local/include/Eigen/src/SVD/SVDBase.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD.h \
		/usr/local/include/Eigen/src/SVD/BDCSVD.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD_LAPACKE.h \
		/usr/local/include/Eigen/Geometry \
		/usr/local/include/Eigen/src/Geometry/OrthoMethods.h \
		/usr/local/include/Eigen/src/Geometry/EulerAngles.h \
		/usr/local/include/Eigen/src/Geometry/Homogeneous.h \
		/usr/local/include/Eigen/src/Geometry/RotationBase.h \
		/usr/local/include/Eigen/src/Geometry/Rotation2D.h \
		/usr/local/include/Eigen/src/Geometry/Quaternion.h \
		/usr/local/include/Eigen/src/Geometry/AngleAxis.h \
		/usr/local/include/Eigen/src/Geometry/Transform.h \
		/usr/local/include/Eigen/src/Geometry/Translation.h \
		/usr/local/include/Eigen/src/Geometry/Scaling.h \
		/usr/local/include/Eigen/src/Geometry/Hyperplane.h \
		/usr/local/include/Eigen/src/Geometry/ParametrizedLine.h \
		/usr/local/include/Eigen/src/Geometry/AlignedBox.h \
		/usr/local/include/Eigen/src/Geometry/Umeyama.h \
		/usr/local/include/Eigen/src/Geometry/arch/Geometry_SIMD.h \
		/usr/local/include/Eigen/Eigenvalues \
		/usr/local/include/Eigen/src/Eigenvalues/Tridiagonalization.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/HessenbergDecomposition.h \
		/usr/local/include/Eigen/src/Eigenvalues/EigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealQZ.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/MatrixBaseEigenvalues.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver_LAPACKE.h \
		../../optimizertypes.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o layer.o ../../layer.cpp

trainingworker.o: ../../trainingworker.cpp ../../trainingworker.h \
		../../mlp.h \
		../../layer.h \
		/usr/local/include/Eigen/Dense \
		/usr/local/include/Eigen/Core \
		/usr/local/include/Eigen/src/Core/util/DisableStupidWarnings.h \
		/usr/local/include/Eigen/src/Core/util/Macros.h \
		/usr/local/include/Eigen/src/Core/util/ConfigureVectorization.h \
		/usr/local/include/Eigen/src/Core/util/MKL_support.h \
		/usr/local/include/Eigen/src/misc/blas.h \
		/usr/local/include/Eigen/src/Core/util/Constants.h \
		/usr/local/include/Eigen/src/Core/util/Meta.h \
		/usr/local/include/Eigen/src/Core/util/ForwardDeclarations.h \
		/usr/local/include/Eigen/src/Core/util/StaticAssert.h \
		/usr/local/include/Eigen/src/Core/util/XprHelper.h \
		/usr/local/include/Eigen/src/Core/util/Memory.h \
		/usr/local/include/Eigen/src/Core/util/IntegralConstant.h \
		/usr/local/include/Eigen/src/Core/util/SymbolicIndex.h \
		/usr/local/include/Eigen/src/Core/NumTraits.h \
		/usr/local/include/Eigen/src/Core/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/GenericPacketMath.h \
		/usr/local/include/Eigen/src/Core/MathFunctionsImpl.h \
		/usr/local/include/Eigen/src/Core/arch/Default/ConjHelper.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Half.h \
		/usr/local/include/Eigen/src/Core/arch/Default/BFloat16.h \
		/usr/local/include/Eigen/src/Core/arch/Default/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctionsFwd.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SSE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AVX512/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SVE/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/ZVector/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/MSA/Complex.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/GPU/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/SyclMemoryModel.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/InteropHeaders.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/PacketMath.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/MathFunctions.h \
		/usr/local/include/Eigen/src/Core/arch/SYCL/TypeCasting.h \
		/usr/local/include/Eigen/src/Core/arch/Default/Settings.h \
		/usr/local/include/Eigen/src/Core/arch/Default/GenericPacketMathFunctions.h \
		/usr/local/include/Eigen/src/Core/functors/TernaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/BinaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/UnaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/NullaryFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/StlFunctors.h \
		/usr/local/include/Eigen/src/Core/functors/AssignmentFunctors.h \
		/usr/local/include/Eigen/src/Core/arch/CUDA/Complex.h \
		/usr/local/include/Eigen/src/Core/util/IndexedViewHelper.h \
		/usr/local/include/Eigen/src/Core/util/ReshapedHelper.h \
		/usr/local/include/Eigen/src/Core/ArithmeticSequence.h \
		/usr/local/include/Eigen/src/Core/IO.h \
		/usr/local/include/Eigen/src/Core/DenseCoeffsBase.h \
		/usr/local/include/Eigen/src/Core/DenseBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/BlockMethods.h \
		/usr/local/include/Eigen/src/plugins/IndexedViewMethods.h \
		/usr/local/include/Eigen/src/plugins/ReshapedMethods.h \
		/usr/local/include/Eigen/src/Core/MatrixBase.h \
		/usr/local/include/Eigen/src/plugins/CommonCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/MatrixCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/EigenBase.h \
		/usr/local/include/Eigen/src/Core/Product.h \
		/usr/local/include/Eigen/src/Core/CoreEvaluators.h \
		/usr/local/include/Eigen/src/Core/AssignEvaluator.h \
		/usr/local/include/Eigen/src/Core/Assign.h \
		/usr/local/include/Eigen/src/Core/ArrayBase.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseUnaryOps.h \
		/usr/local/include/Eigen/src/plugins/ArrayCwiseBinaryOps.h \
		/usr/local/include/Eigen/src/Core/util/BlasUtil.h \
		/usr/local/include/Eigen/src/Core/DenseStorage.h \
		/usr/local/include/Eigen/src/Core/NestByValue.h \
		/usr/local/include/Eigen/src/Core/ReturnByValue.h \
		/usr/local/include/Eigen/src/Core/NoAlias.h \
		/usr/local/include/Eigen/src/Core/PlainObjectBase.h \
		/usr/local/include/Eigen/src/Core/Matrix.h \
		/usr/local/include/Eigen/src/Core/Array.h \
		/usr/local/include/Eigen/src/Core/CwiseTernaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseNullaryOp.h \
		/usr/local/include/Eigen/src/Core/CwiseUnaryView.h \
		/usr/local/include/Eigen/src/Core/SelfCwiseBinaryOp.h \
		/usr/local/include/Eigen/src/Core/Dot.h \
		/usr/local/include/Eigen/src/Core/StableNorm.h \
		/usr/local/include/Eigen/src/Core/Stride.h \
		/usr/local/include/Eigen/src/Core/MapBase.h \
		/usr/local/include/Eigen/src/Core/Map.h \
		/usr/local/include/Eigen/src/Core/Ref.h \
		/usr/local/include/Eigen/src/Core/Block.h \
		/usr/local/include/Eigen/src/Core/VectorBlock.h \
		/usr/local/include/Eigen/src/Core/IndexedView.h \
		/usr/local/include/Eigen/src/Core/Reshaped.h \
		/usr/local/include/Eigen/src/Core/Transpose.h \
		/usr/local/include/Eigen/src/Core/DiagonalMatrix.h \
		/usr/local/include/Eigen/src/Core/Diagonal.h \
		/usr/local/include/Eigen/src/Core/DiagonalProduct.h \
		/usr/local/include/Eigen/src/Core/Redux.h \
		/usr/local/include/Eigen/src/Core/Visitor.h \
		/usr/local/include/Eigen/src/Core/Fuzzy.h \
		/usr/local/include/Eigen/src/Core/Swap.h \
		/usr/local/include/Eigen/src/Core/CommaInitializer.h \
		/usr/local/include/Eigen/src/Core/GeneralProduct.h \
		/usr/local/include/Eigen/src/Core/Solve.h \
		/usr/local/include/Eigen/src/Core/Inverse.h \
		/usr/local/include/Eigen/src/Core/SolverBase.h \
		/usr/local/include/Eigen/src/Core/PermutationMatrix.h \
		/usr/local/include/Eigen/src/Core/Transpositions.h \
		/usr/local/include/Eigen/src/Core/TriangularMatrix.h \
		/usr/local/include/Eigen/src/Core/SelfAdjointView.h \
		/usr/local/include/Eigen/src/Core/products/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/products/Parallelizer.h \
		/usr/local/include/Eigen/src/Core/ProductEvaluators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/SolveTriangular.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointProduct.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointRank2Update.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverVector.h \
		/usr/local/include/Eigen/src/Core/BandMatrix.h \
		/usr/local/include/Eigen/src/Core/CoreIterators.h \
		/usr/local/include/Eigen/src/Core/ConditionEstimator.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProduct.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductCommon.h \
		/usr/local/include/Eigen/src/Core/arch/AltiVec/MatrixProductMMA.h \
		/usr/local/include/Eigen/src/Core/arch/NEON/GeneralBlockPanelKernel.h \
		/usr/local/include/Eigen/src/Core/BooleanRedux.h \
		/usr/local/include/Eigen/src/Core/Select.h \
		/usr/local/include/Eigen/src/Core/VectorwiseOp.h \
		/usr/local/include/Eigen/src/Core/PartialReduxEvaluator.h \
		/usr/local/include/Eigen/src/Core/Random.h \
		/usr/local/include/Eigen/src/Core/Replicate.h \
		/usr/local/include/Eigen/src/Core/Reverse.h \
		/usr/local/include/Eigen/src/Core/ArrayWrapper.h \
		/usr/local/include/Eigen/src/Core/StlIterators.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/SelfadjointMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularMatrixVector_BLAS.h \
		/usr/local/include/Eigen/src/Core/products/TriangularSolverMatrix_BLAS.h \
		/usr/local/include/Eigen/src/Core/Assign_MKL.h \
		/usr/local/include/Eigen/src/Core/GlobalFunctions.h \
		/usr/local/include/Eigen/src/Core/util/ReenableStupidWarnings.h \
		/usr/local/include/Eigen/LU \
		/usr/local/include/Eigen/src/misc/Kernel.h \
		/usr/local/include/Eigen/src/misc/Image.h \
		/usr/local/include/Eigen/src/LU/FullPivLU.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU.h \
		/usr/local/include/Eigen/src/misc/lapacke.h \
		/usr/local/include/Eigen/src/misc/lapacke_mangling.h \
		/usr/local/include/Eigen/src/LU/PartialPivLU_LAPACKE.h \
		/usr/local/include/Eigen/src/LU/Determinant.h \
		/usr/local/include/Eigen/src/LU/InverseImpl.h \
		/usr/local/include/Eigen/src/LU/arch/InverseSize4.h \
		/usr/local/include/Eigen/Cholesky \
		/usr/local/include/Eigen/Jacobi \
		/usr/local/include/Eigen/src/Jacobi/Jacobi.h \
		/usr/local/include/Eigen/src/Cholesky/LLT.h \
		/usr/local/include/Eigen/src/Cholesky/LDLT.h \
		/usr/local/include/Eigen/src/Cholesky/LLT_LAPACKE.h \
		/usr/local/include/Eigen/QR \
		/usr/local/include/Eigen/Householder \
		/usr/local/include/Eigen/src/Householder/Householder.h \
		/usr/local/include/Eigen/src/Householder/HouseholderSequence.h \
		/usr/local/include/Eigen/src/Householder/BlockHouseholder.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR.h \
		/usr/local/include/Eigen/src/QR/FullPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR.h \
		/usr/local/include/Eigen/src/QR/CompleteOrthogonalDecomposition.h \
		/usr/local/include/Eigen/src/QR/HouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/src/QR/ColPivHouseholderQR_LAPACKE.h \
		/usr/local/include/Eigen/SVD \
		/usr/local/include/Eigen/src/misc/RealSvd2x2.h \
		/usr/local/include/Eigen/src/SVD/UpperBidiagonalization.h \
		/usr/local/include/Eigen/src/SVD/SVDBase.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD.h \
		/usr/local/include/Eigen/src/SVD/BDCSVD.h \
		/usr/local/include/Eigen/src/SVD/JacobiSVD_LAPACKE.h \
		/usr/local/include/Eigen/Geometry \
		/usr/local/include/Eigen/src/Geometry/OrthoMethods.h \
		/usr/local/include/Eigen/src/Geometry/EulerAngles.h \
		/usr/local/include/Eigen/src/Geometry/Homogeneous.h \
		/usr/local/include/Eigen/src/Geometry/RotationBase.h \
		/usr/local/include/Eigen/src/Geometry/Rotation2D.h \
		/usr/local/include/Eigen/src/Geometry/Quaternion.h \
		/usr/local/include/Eigen/src/Geometry/AngleAxis.h \
		/usr/local/include/Eigen/src/Geometry/Transform.h \
		/usr/local/include/Eigen/src/Geometry/Translation.h \
		/usr/local/include/Eigen/src/Geometry/Scaling.h \
		/usr/local/include/Eigen/src/Geometry/Hyperplane.h \
		/usr/local/include/Eigen/src/Geometry/ParametrizedLine.h \
		/usr/local/include/Eigen/src/Geometry/AlignedBox.h \
		/usr/local/include/Eigen/src/Geometry/Umeyama.h \
		/usr/local/include/Eigen/src/Geometry/arch/Geometry_SIMD.h \
		/usr/local/include/Eigen/Eigenvalues \
		/usr/local/include/Eigen/src/Eigenvalues/Tridiagonalization.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/HessenbergDecomposition.h \
		/usr/local/include/Eigen/src/Eigenvalues/EigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealQZ.h \
		/usr/local/include/Eigen/src/Eigenvalues/GeneralizedEigenSolver.h \
		/usr/local/include/Eigen/src/Eigenvalues/MatrixBaseEigenvalues.h \
		/usr/local/include/Eigen/src/Eigenvalues/RealSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/ComplexSchur_LAPACKE.h \
		/usr/local/include/Eigen/src/Eigenvalues/SelfAdjointEigenSolver_LAPACKE.h \
		../../optimizertypes.h \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/QImage \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/qimage.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonObject \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsonobject.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QFile \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qfile.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDataStream \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdatastream.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QByteArray \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qbytearray.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QJsonDocument \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qjsondocument.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutex \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qmutex.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QMutexLocker \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QObject \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qobject.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QThread \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qthread.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QWaitCondition \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qwaitcondition.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDir \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdir.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDirIterator \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdiriterator.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QFileInfo \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qfileinfo.h \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/QImageReader \
		/opt/Qt/6.9.1/macos/lib/QtGui.framework/Headers/qimagereader.h \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/QDebug \
		/opt/Qt/6.9.1/macos/lib/QtCore.framework/Headers/qdebug.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o trainingworker.o ../../trainingworker.cpp

losscurvewidget.o: ../../losscurvewidget.cpp ../../losscurvewidget.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o losscurvewidget.o ../../losscurvewidget.cpp

qrc_qmake_qmake_qm_files.o: qrc_qmake_qmake_qm_files.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qrc_qmake_qmake_qm_files.o qrc_qmake_qmake_qm_files.cpp

moc_mainwindow.o: moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_mainwindow.o moc_mainwindow.cpp

moc_trainingworker.o: moc_trainingworker.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_trainingworker.o moc_trainingworker.cpp

moc_losscurvewidget.o: moc_losscurvewidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_losscurvewidget.o moc_losscurvewidget.cpp

####### Install

install_target: first FORCE
	@test -d $(INSTALL_ROOT)/opt/sensuser/bin || mkdir -p $(INSTALL_ROOT)/opt/sensuser/bin
	$(DEL_FILE) -r $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app
	
	$(QINSTALL) sensuser.app $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app

uninstall_target: FORCE
	-$(DEL_FILE) -r $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app
	-$(DEL_DIR) $(INSTALL_ROOT)/opt/sensuser/bin/ 


install_debug_info_plist_target: first FORCE
	@test -d $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app.dSYM/Contents || mkdir -p $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app.dSYM/Contents
	$(QINSTALL) /Users/<USER>/Desktop/projects/sensuser/repo/sensuser/build/Qt_6_9_1_for_macOS-Release/sensuser.app.dSYM/Contents/Info.plist $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app.dSYM/Contents/Info.plist

uninstall_debug_info_plist_target: FORCE
	-$(DEL_FILE) -r $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app.dSYM/Contents/Info.plist
	-$(DEL_DIR) $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app.dSYM/Contents/ 


install_debug_info_target: first FORCE
	@test -d $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app.dSYM/Contents/Resources/DWARF || mkdir -p $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app.dSYM/Contents/Resources/DWARF
	$(QINSTALL) /Users/<USER>/Desktop/projects/sensuser/repo/sensuser/build/Qt_6_9_1_for_macOS-Release/sensuser.app.dSYM/Contents/Resources/DWARF/sensuser $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app.dSYM/Contents/Resources/DWARF/sensuser

uninstall_debug_info_target: FORCE
	-$(DEL_FILE) -r $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app.dSYM/Contents/Resources/DWARF/sensuser
	-$(DEL_DIR) $(INSTALL_ROOT)/opt/sensuser/bin/sensuser.app.dSYM/Contents/Resources/DWARF/ 


install: install_target install_debug_info_plist_target install_debug_info_target  FORCE

uninstall: uninstall_target uninstall_debug_info_plist_target uninstall_debug_info_target  FORCE

FORCE:

.SUFFIXES:

