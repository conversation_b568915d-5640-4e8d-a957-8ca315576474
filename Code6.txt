-------------------------------------
Translated Report (Full Report Below)
-------------------------------------

Process:               sensuser [17087]
Path:                  /Users/<USER>/Desktop/*/sensuser.app/Contents/MacOS/sensuser
Identifier:            com.andrewjosephsmith.noodlenet-cpp.sensuser
Version:               ???
Code Type:             ARM-64 (Native)
Parent Process:        launchd [1]
User ID:               501

Date/Time:             2025-06-08 10:29:57.4920 -0400
OS Version:            macOS 15.5 (24F74)
Report Version:        12
Anonymous UUID:        43D83CEF-E391-39B0-2A34-C64877BFD9C6


Time Awake Since Boot: 2900 seconds

System Integrity Protection: enabled

Crashed Thread:        1  QThread

Exception Type:        EXC_CRASH (SIGABRT)
Exception Codes:       0x0000000000000000, 0x0000000000000000

Termination Reason:    Namespace SIGNAL, Code 6 Abort trap: 6
Terminating Process:   sensuser [17087]

Application Specific Information:
abort() called


Thread 0::  Dispatch queue: com.apple.main-thread
0   sensuser                      	       0x104991c04 Eigen::internal::general_matrix_vector_product<long, float, Eigen::internal::const_blas_data_mapper<float, long, 0>, 0, false, float, Eigen::internal::const_blas_data_mapper<float, long, 1>, false, 0>::run(long, long, Eigen::internal::const_blas_data_mapper<float, long, 0> const&, Eigen::internal::const_blas_data_mapper<float, long, 1> const&, float*, long, float) + 428 (GeneralMatrixVector.h:167)
1   sensuser                      	       0x1049916d4 void Eigen::internal::generic_product_impl_base<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::internal::generic_product_impl<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::DenseShape, Eigen::DenseShape, 7>>::scaleAndAddTo<Eigen::Matrix<float, -1, 1, 0, -1, 1>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::Matrix<float, -1, -1, 0, -1, -1> const&, Eigen::Matrix<float, -1, 1, 0, -1, 1> const&, float const&) + 16 (ProductEvaluators.h:361) [inlined]
2   sensuser                      	       0x1049916d4 void Eigen::internal::generic_product_impl_base<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::internal::generic_product_impl<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::DenseShape, Eigen::DenseShape, 7>>::evalTo<Eigen::Matrix<float, -1, 1, 0, -1, 1>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::Matrix<float, -1, -1, 0, -1, -1> const&, Eigen::Matrix<float, -1, 1, 0, -1, 1> const&) + 16 (ProductEvaluators.h:349) [inlined]
3   sensuser                      	       0x1049916d4 Eigen::internal::product_evaluator<Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0>, 7, Eigen::DenseShape, Eigen::DenseShape, float, float>::product_evaluator(Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const&) + 100 (ProductEvaluators.h:124) [inlined]
4   sensuser                      	       0x1049916d4 Eigen::internal::evaluator<Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0>>::evaluator(Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const&) + 100 (ProductEvaluators.h:35) [inlined]
5   sensuser                      	       0x1049916d4 Eigen::internal::evaluator<Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const>::evaluator(Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const&) + 100 (CoreEvaluators.h:104) [inlined]
6   sensuser                      	       0x1049916d4 Eigen::internal::evaluator<Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const>::evaluator(Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const&) + 100 (CoreEvaluators.h:104) [inlined]
7   sensuser                      	       0x1049916d4 Eigen::internal::binary_evaluator<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::IndexBased, Eigen::internal::IndexBased, float, float>::Data::Data(Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&) + 100 (CoreEvaluators.h:800) [inlined]
8   sensuser                      	       0x1049916d4 Eigen::internal::binary_evaluator<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::IndexBased, Eigen::internal::IndexBased, float, float>::Data::Data(Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&) + 100 (CoreEvaluators.h:800) [inlined]
9   sensuser                      	       0x1049916d4 Eigen::internal::binary_evaluator<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::IndexBased, Eigen::internal::IndexBased, float, float>::binary_evaluator(Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&) + 100 (CoreEvaluators.h:758) [inlined]
10  sensuser                      	       0x1049916d4 Eigen::internal::evaluator<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>::evaluator(Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&) + 100 (CoreEvaluators.h:729) [inlined]
11  sensuser                      	       0x1049916d4 Eigen::internal::evaluator<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>::evaluator(Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&) + 100 (CoreEvaluators.h:729) [inlined]
12  sensuser                      	       0x1049916d4 void Eigen::internal::call_dense_assignment_loop<Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::assign_op<float, float>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&, Eigen::internal::assign_op<float, float> const&) + 140 (AssignEvaluator.h:774)
13  sensuser                      	       0x10498e810 Eigen::internal::Assignment<Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::assign_op<float, float>, Eigen::internal::Dense2Dense, void>::run(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&, Eigen::internal::assign_op<float, float> const&) + 12 (AssignEvaluator.h:954) [inlined]
14  sensuser                      	       0x10498e810 void Eigen::internal::call_assignment_no_alias<Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::assign_op<float, float>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&, Eigen::internal::assign_op<float, float> const&) + 12 (AssignEvaluator.h:890) [inlined]
15  sensuser                      	       0x10498e810 void Eigen::internal::call_assignment<Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::assign_op<float, float>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&, Eigen::internal::assign_op<float, float> const&, Eigen::internal::enable_if<!evaluator_assume_aliasing<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>::value, void*>::type) + 12 (AssignEvaluator.h:858) [inlined]
16  sensuser                      	       0x10498e810 void Eigen::internal::call_assignment<Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&) + 12 (AssignEvaluator.h:836) [inlined]
17  sensuser                      	       0x10498e810 Eigen::Matrix<float, -1, 1, 0, -1, 1>& Eigen::PlainObjectBase<Eigen::Matrix<float, -1, 1, 0, -1, 1>>::_set<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>(Eigen::DenseBase<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>> const&) + 12 (PlainObjectBase.h:779) [inlined]
18  sensuser                      	       0x10498e810 Eigen::Matrix<float, -1, 1, 0, -1, 1>& Eigen::Matrix<float, -1, 1, 0, -1, 1>::operator=<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>(Eigen::DenseBase<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>> const&) + 12 (Matrix.h:225) [inlined]
19  sensuser                      	       0x10498e810 Layer::forward(Eigen::Matrix<float, -1, 1, 0, -1, 1> const&) + 356 (layer.cpp:89)
20  sensuser                      	       0x104987d4c MLP::forward(Eigen::Matrix<float, -1, 1, 0, -1, 1> const&) + 92 (mlp.cpp:75)
21  sensuser                      	       0x10497e470 MainWindow::updateHiddenLayerVisualization() + 120 (mainwindow.cpp:316)
22  sensuser                      	       0x10497dfe4 MainWindow::updateLayerVisualizations() + 56 (mainwindow.cpp:248)
23  QtCore                        	       0x106dca200 0x106ce0000 + 958976
24  QtWidgets                     	       0x105d79444 0x105b74000 + 2118724
25  QtCore                        	       0x106dca1b4 0x106ce0000 + 958900
26  QtWidgets                     	       0x105d5b1cc QTabBar::setCurrentIndex(int) + 448
27  QtWidgets                     	       0x105d5f068 QTabBar::mouseReleaseEvent(QMouseEvent*) + 452
28  QtWidgets                     	       0x105bd0f1c QWidget::event(QEvent*) + 132
29  QtWidgets                     	       0x105d5d0d8 QTabBar::event(QEvent*) + 800
30  QtWidgets                     	       0x105b83c74 QApplicationPrivate::notify_helper(QObject*, QEvent*) + 332
31  QtWidgets                     	       0x105b85df0 QApplication::notify(QObject*, QEvent*) + 5032
32  QtCore                        	       0x106d790e4 QCoreApplication::sendSpontaneousEvent(QObject*, QEvent*) + 176
33  QtWidgets                     	       0x105b842cc QApplicationPrivate::sendMouseEvent(QWidget*, QMouseEvent*, QWidget*, QWidget*, QWidget**, QPointer<QWidget>&, bool, bool) + 892
34  QtWidgets                     	       0x105be6d60 0x105b74000 + 470368
35  QtWidgets                     	       0x105be6008 0x105b74000 + 466952
36  QtWidgets                     	       0x105b83c74 QApplicationPrivate::notify_helper(QObject*, QEvent*) + 332
37  QtWidgets                     	       0x105b84c4c QApplication::notify(QObject*, QEvent*) + 516
38  QtCore                        	       0x106d790e4 QCoreApplication::sendSpontaneousEvent(QObject*, QEvent*) + 176
39  QtGui                         	       0x104fef834 QGuiApplicationPrivate::processMouseEvent(QWindowSystemInterfacePrivate::MouseEvent*) + 1924
40  QtGui                         	       0x105051478 QWindowSystemInterface::sendWindowSystemEvents(QFlags<QEventLoop::ProcessEventsFlag>) + 408
41  libqcocoa.dylib               	       0x105a3d7e4 0x105a24000 + 104420
42  CoreFoundation                	       0x18f30dcd4 __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__ + 28
43  CoreFoundation                	       0x18f30dc68 __CFRunLoopDoSource0 + 172
44  CoreFoundation                	       0x18f30d9d4 __CFRunLoopDoSources0 + 232
45  CoreFoundation                	       0x18f30c628 __CFRunLoopRun + 840
46  CoreFoundation                	       0x18f30bc58 CFRunLoopRunSpecific + 572
47  HIToolbox                     	       0x19ada027c RunCurrentEventLoopInMode + 324
48  HIToolbox                     	       0x19ada331c ReceiveNextEventCommon + 216
49  HIToolbox                     	       0x19af2e484 _BlockUntilNextEventMatchingListInModeWithFilter + 76
50  AppKit                        	       0x193233ab4 _DPSNextEvent + 684
51  AppKit                        	       0x193bd25b0 -[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:] + 688
52  AppKit                        	       0x193226c64 -[NSApplication run] + 480
53  libqcocoa.dylib               	       0x105a3b068 0x105a24000 + 94312
54  QtCore                        	       0x106d8267c QEventLoop::exec(QFlags<QEventLoop::ProcessEventsFlag>) + 588
55  QtCore                        	       0x106d78cc8 QCoreApplication::exec() + 228
56  sensuser                      	       0x104978edc main + 596 (main.cpp:22)
57  dyld                          	       0x18ee82b98 start + 6076

Thread 1 Crashed:: QThread
0   libsystem_kernel.dylib        	       0x18f1e9388 __pthread_kill + 8
1   libsystem_pthread.dylib       	       0x18f22288c pthread_kill + 296
2   libsystem_c.dylib             	       0x18f12bc60 abort + 124
3   libsystem_malloc.dylib        	       0x18f030174 malloc_vreport + 892
4   libsystem_malloc.dylib        	       0x18f05ba00 malloc_zone_error + 100
5   libsystem_malloc.dylib        	       0x18f03d550 <deduplicated_symbol> + 40
6   sensuser                      	       0x10498e83c Eigen::internal::aligned_free(void*) + 8 (Memory.h:203) [inlined]
7   sensuser                      	       0x10498e83c void Eigen::internal::conditional_aligned_free<true>(void*) + 8 (Memory.h:259) [inlined]
8   sensuser                      	       0x10498e83c void Eigen::internal::conditional_aligned_delete_auto<float, true>(float*, unsigned long) + 8 (Memory.h:446) [inlined]
9   sensuser                      	       0x10498e83c Eigen::DenseStorage<float, -1, -1, 1, 0>::~DenseStorage() + 8 (DenseStorage.h:621) [inlined]
10  sensuser                      	       0x10498e83c Eigen::DenseStorage<float, -1, -1, 1, 0>::~DenseStorage() + 8 (DenseStorage.h:621) [inlined]
11  sensuser                      	       0x10498e83c Eigen::PlainObjectBase<Eigen::Matrix<float, -1, 1, 0, -1, 1>>::~PlainObjectBase() + 8 (PlainObjectBase.h:98) [inlined]
12  sensuser                      	       0x10498e83c Eigen::Matrix<float, -1, 1, 0, -1, 1>::~Matrix() + 8 (Matrix.h:178) [inlined]
13  sensuser                      	       0x10498e83c Eigen::Matrix<float, -1, 1, 0, -1, 1>::~Matrix() + 8 (Matrix.h:178) [inlined]
14  sensuser                      	       0x10498e83c Layer::forward(Eigen::Matrix<float, -1, 1, 0, -1, 1> const&) + 400 (layer.cpp:92)
15  sensuser                      	       0x104987d4c MLP::forward(Eigen::Matrix<float, -1, 1, 0, -1, 1> const&) + 92 (mlp.cpp:75)
16  sensuser                      	       0x104987de8 MLP::train(Eigen::Matrix<float, -1, 1, 0, -1, 1> const&, Eigen::Matrix<float, -1, 1, 0, -1, 1> const&, float, OptimizerType, int) + 72 (mlp.cpp:91)
17  sensuser                      	       0x104995bfc TrainingWorker::train() + 2256 (trainingworker.cpp:212)
18  QtCore                        	       0x106dc2244 QObject::event(QEvent*) + 672
19  QtWidgets                     	       0x105b83c74 QApplicationPrivate::notify_helper(QObject*, QEvent*) + 332
20  QtWidgets                     	       0x105b84c4c QApplication::notify(QObject*, QEvent*) + 516
21  QtCore                        	       0x106d78fb8 QCoreApplication::sendEvent(QObject*, QEvent*) + 172
22  QtCore                        	       0x106d79838 QCoreApplicationPrivate::sendPostedEvents(QObject*, int, QThreadData*) + 524
23  QtCore                        	       0x106f068b0 QEventDispatcherUNIX::processEvents(QFlags<QEventLoop::ProcessEventsFlag>) + 84
24  QtCore                        	       0x106d8267c QEventLoop::exec(QFlags<QEventLoop::ProcessEventsFlag>) + 588
25  QtCore                        	       0x106e6ef50 QThread::exec() + 332
26  QtCore                        	       0x106f0483c 0x106ce0000 + 2246716
27  libsystem_pthread.dylib       	       0x18f222c0c _pthread_start + 136
28  libsystem_pthread.dylib       	       0x18f21db80 thread_start + 8

Thread 2:: com.apple.NSEventThread
0   libsystem_kernel.dylib        	       0x18f1e0c34 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x18f1f33a0 mach_msg2_internal + 76
2   libsystem_kernel.dylib        	       0x18f1e9764 mach_msg_overwrite + 484
3   libsystem_kernel.dylib        	       0x18f1e0fa8 mach_msg + 24
4   CoreFoundation                	       0x18f30de7c __CFRunLoopServiceMachPort + 160
5   CoreFoundation                	       0x18f30c798 __CFRunLoopRun + 1208
6   CoreFoundation                	       0x18f30bc58 CFRunLoopRunSpecific + 572
7   AppKit                        	       0x1933577fc _NSEventThread + 140
8   libsystem_pthread.dylib       	       0x18f222c0c _pthread_start + 136
9   libsystem_pthread.dylib       	       0x18f21db80 thread_start + 8

Thread 3:
0   libsystem_pthread.dylib       	       0x18f21db6c start_wqthread + 0

Thread 4:: Thread (pooled)
0   libsystem_kernel.dylib        	       0x18f1e43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x18f2230e0 _pthread_cond_wait + 984
2   QtCore                        	       0x106f124d8 0x106ce0000 + 2303192
3   QtCore                        	       0x106f12368 QWaitCondition::wait(QMutex*, QDeadlineTimer) + 108
4   QtCore                        	       0x106f0c920 0x106ce0000 + 2279712
5   QtCore                        	       0x106f0483c 0x106ce0000 + 2246716
6   libsystem_pthread.dylib       	       0x18f222c0c _pthread_start + 136
7   libsystem_pthread.dylib       	       0x18f21db80 thread_start + 8

Thread 5:: Thread (pooled)
0   libsystem_kernel.dylib        	       0x18f1e43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x18f2230e0 _pthread_cond_wait + 984
2   QtCore                        	       0x106f124d8 0x106ce0000 + 2303192
3   QtCore                        	       0x106f12368 QWaitCondition::wait(QMutex*, QDeadlineTimer) + 108
4   QtCore                        	       0x106f0c920 0x106ce0000 + 2279712
5   QtCore                        	       0x106f0483c 0x106ce0000 + 2246716
6   libsystem_pthread.dylib       	       0x18f222c0c _pthread_start + 136
7   libsystem_pthread.dylib       	       0x18f21db80 thread_start + 8

Thread 6:: Thread (pooled)
0   libsystem_kernel.dylib        	       0x18f1e43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x18f2230e0 _pthread_cond_wait + 984
2   QtCore                        	       0x106f124d8 0x106ce0000 + 2303192
3   QtCore                        	       0x106f12368 QWaitCondition::wait(QMutex*, QDeadlineTimer) + 108
4   QtCore                        	       0x106f0c920 0x106ce0000 + 2279712
5   QtCore                        	       0x106f0483c 0x106ce0000 + 2246716
6   libsystem_pthread.dylib       	       0x18f222c0c _pthread_start + 136
7   libsystem_pthread.dylib       	       0x18f21db80 thread_start + 8

Thread 7:: Thread (pooled)
0   libsystem_kernel.dylib        	       0x18f1e43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x18f2230e0 _pthread_cond_wait + 984
2   QtCore                        	       0x106f124d8 0x106ce0000 + 2303192
3   QtCore                        	       0x106f12368 QWaitCondition::wait(QMutex*, QDeadlineTimer) + 108
4   QtCore                        	       0x106f0c920 0x106ce0000 + 2279712
5   QtCore                        	       0x106f0483c 0x106ce0000 + 2246716
6   libsystem_pthread.dylib       	       0x18f222c0c _pthread_start + 136
7   libsystem_pthread.dylib       	       0x18f21db80 thread_start + 8

Thread 8:: Thread (pooled)
0   libsystem_kernel.dylib        	       0x18f1e43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x18f2230e0 _pthread_cond_wait + 984
2   QtCore                        	       0x106f124d8 0x106ce0000 + 2303192
3   QtCore                        	       0x106f12368 QWaitCondition::wait(QMutex*, QDeadlineTimer) + 108
4   QtCore                        	       0x106f0c920 0x106ce0000 + 2279712
5   QtCore                        	       0x106f0483c 0x106ce0000 + 2246716
6   libsystem_pthread.dylib       	       0x18f222c0c _pthread_start + 136
7   libsystem_pthread.dylib       	       0x18f21db80 thread_start + 8

Thread 9:: Thread (pooled)
0   libsystem_kernel.dylib        	       0x18f1e43cc __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x18f2230e0 _pthread_cond_wait + 984
2   QtCore                        	       0x106f124d8 0x106ce0000 + 2303192
3   QtCore                        	       0x106f12368 QWaitCondition::wait(QMutex*, QDeadlineTimer) + 108
4   QtCore                        	       0x106f0c920 0x106ce0000 + 2279712
5   QtCore                        	       0x106f0483c 0x106ce0000 + 2246716
6   libsystem_pthread.dylib       	       0x18f222c0c _pthread_start + 136
7   libsystem_pthread.dylib       	       0x18f21db80 thread_start + 8

Thread 10:
0   libsystem_pthread.dylib       	       0x18f21db6c start_wqthread + 0


Thread 1 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000000
    x4: 0x0000000000000073   x5: 0x000000000000002e   x6: 0x0000000000000001   x7: 0x0000000114838028
    x8: 0x3bae301451c89b49   x9: 0x3bae301522b46b49  x10: 0x000000000000000a  x11: 0x0000000000000000
   x12: 0x0000000000000031  x13: 0x0000000000000031  x14: 0x9e3779b97f4a7c55  x15: 0x000000011ecab800
   x16: 0x0000000000000148  x17: 0x00000001fe381fa8  x18: 0x0000000000000000  x19: 0x0000000000000006
   x20: 0x000000000000a703  x21: 0x00000001737cf0e0  x22: 0x000000018f060972  x23: 0x00000001737cdd60
   x24: 0x0000000000000000  x25: 0x0000000000000000  x26: 0x000000016b48bba8  x27: 0x00000001737cf000
   x28: 0x0000000000000010   fp: 0x00000001737cd6b0   lr: 0x000000018f22288c
    sp: 0x00000001737cd690   pc: 0x000000018f1e9388 cpsr: 0x40001000
   far: 0x0000000000000000  esr: 0x56000080  Address size fault

Binary Images:
       0x104974000 -        0x1049a3fff com.andrewjosephsmith.noodlenet-cpp.sensuser (*) <68d9e804-55bf-34cd-830c-87e9d4bdbb04> /Users/<USER>/Desktop/*/sensuser.app/Contents/MacOS/sensuser
       0x104dbc000 -        0x104eb3fff org.qt-project.QtCharts (6.9) <95df4f4e-d558-3602-afb9-42424d803857> /opt/Qt/*/QtCharts.framework/Versions/A/QtCharts
       0x104a10000 -        0x104a1bfff org.qt-project.QtOpenGLWidgets (6.9) <21309296-564a-3214-894b-5cb50594905b> /opt/Qt/*/QtOpenGLWidgets.framework/Versions/A/QtOpenGLWidgets
       0x105b74000 -        0x105fdffff org.qt-project.QtWidgets (6.9) <e84cfcf9-d145-312e-885c-159d40dfbd9e> /opt/Qt/*/QtWidgets.framework/Versions/A/QtWidgets
       0x104b6c000 -        0x104bd3fff org.qt-project.QtOpenGL (6.9) <a5c4c691-be77-3d0a-a711-c0da2c7ebbe5> /opt/Qt/*/QtOpenGL.framework/Versions/A/QtOpenGL
       0x104f60000 -        0x1055fffff org.qt-project.QtGui (6.9) <4f544aad-06c2-3315-8261-1c9e3a7e3082> /opt/Qt/*/QtGui.framework/Versions/A/QtGui
       0x106ce0000 -        0x107193fff org.qt-project.QtCore (6.9) <115f06be-4c99-3fa9-8699-1605ab53b730> /opt/Qt/*/QtCore.framework/Versions/A/QtCore
       0x104a30000 -        0x104ab3fff org.qt-project.QtDBus (6.9) <bd131634-af8c-3140-b3ef-21475baa7c1d> /opt/Qt/*/QtDBus.framework/Versions/A/QtDBus
       0x104c74000 -        0x104c93fff com.apple.security.csparser (3.0) <c12848ee-0663-3987-842f-8832599d139f> /System/Library/Frameworks/Security.framework/Versions/A/PlugIns/csparser.bundle/Contents/MacOS/csparser
       0x105a24000 -        0x105acbfff libqcocoa.dylib (*) <7cdd975f-1e6c-316b-a344-dec675d31e1b> /opt/Qt/*/libqcocoa.dylib
       0x106974000 -        0x10697ffff libobjc-trampolines.dylib (*) <d02a05cb-6440-3e7e-a02f-931734cab666> /usr/lib/libobjc-trampolines.dylib
       0x114064000 -        0x114087fff libqmacstyle.dylib (*) <e337df1c-6c8f-3c8f-801b-d23394f34e54> /opt/Qt/*/libqmacstyle.dylib
       0x1156e4000 -        0x115debfff com.apple.AGXMetalG14X (327.5) <da19826d-64c0-3f82-97cc-7ae98b831d91> /System/Library/Extensions/AGXMetalG14X.bundle/Contents/MacOS/AGXMetalG14X
       0x18f291000 -        0x18f7cffff com.apple.CoreFoundation (6.9) <df489a59-b4f6-32b8-9bb4-9b832960aa52> /System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
       0x19acdd000 -        0x19afe3fdf com.apple.HIToolbox (2.1.1) <9286e29f-fcee-31d0-acea-2842ea23bedf> /System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
       0x1931f9000 -        0x19468ac7f com.apple.AppKit (6.9) <5d0da1bd-412c-3ed8-84e9-40ca62fe7b42> /System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
       0x18ee7c000 -        0x18ef174cf dyld (*) <9cf0401a-a938-389e-a77d-9e9608076ccf> /usr/lib/dyld
               0x0 - 0xffffffffffffffff ??? (*) <00000000-0000-0000-0000-000000000000> ???
       0x18f1e0000 -        0x18f21b653 libsystem_kernel.dylib (*) <60485b6f-67e5-38c1-aec9-efd6031ff166> /usr/lib/system/libsystem_kernel.dylib
       0x18f21c000 -        0x18f228a47 libsystem_pthread.dylib (*) <647b91fc-96d3-3bbb-af08-970df45257c8> /usr/lib/system/libsystem_pthread.dylib
       0x18f0b3000 -        0x18f13446f libsystem_c.dylib (*) <f4529d5e-24f3-3bbb-bd3c-984856875fc8> /usr/lib/system/libsystem_c.dylib
       0x18f022000 -        0x18f068fef libsystem_malloc.dylib (*) <e2c4cbe4-6195-3328-b87c-2dfa4a6ad039> /usr/lib/system/libsystem_malloc.dylib

External Modification Summary:
  Calls made by other processes targeting this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by this process:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0
  Calls made by all processes on this machine:
    task_for_pid: 0
    thread_create: 0
    thread_set_state: 0

VM Region Summary:
ReadOnly portion of Libraries: Total=1.7G resident=0K(0%) swapped_out_or_unallocated=1.7G(100%)
Writable regions: Total=4.5G written=2104K(0%) resident=2104K(0%) swapped_out=0K(0%) unallocated=4.5G(100%)

                                VIRTUAL   REGION 
REGION TYPE                        SIZE    COUNT (non-coalesced) 
===========                     =======  ======= 
Accelerate framework               256K        2 
Activity Tracing                   256K        1 
CG image                           208K       11 
ColorSync                          640K       29 
CoreAnimation                      464K       29 
CoreGraphics                        48K        3 
CoreUI image data                 1760K       22 
Foundation                          16K        1 
Kernel Alloc Once                   32K        1 
MALLOC                             4.5G       99 
MALLOC guard page                  288K       18 
STACK GUARD                       56.2M       11 
Stack                             13.3M       12 
VM_ALLOCATE                        256K       16 
__AUTH                            5367K      683 
__AUTH_CONST                      75.9M      924 
__CTF                               824        1 
__DATA                            25.5M      917 
__DATA_CONST                      27.3M      944 
__DATA_DIRTY                      2763K      335 
__FONT_DATA                        2352        1 
__INFO_FILTER                         8        1 
__LINKEDIT                       621.4M       14 
__OBJC_RO                         61.4M        1 
__OBJC_RW                         2396K        1 
__TEXT                             1.1G      964 
__TPRO_CONST                       128K        2 
mapped file                      224.6M       29 
page table in kernel              2104K        1 
shared memory                     1392K       14 
===========                     =======  ======= 
TOTAL                              6.7G     5087 



-----------
Full Report
-----------

{"app_name":"sensuser","timestamp":"2025-06-08 10:30:14.00 -0400","app_version":"","slice_uuid":"68d9e804-55bf-34cd-830c-87e9d4bdbb04","build_version":"","platform":1,"bundleID":"com.andrewjosephsmith.noodlenet-cpp.sensuser","share_with_app_devs":1,"is_first_party":0,"bug_type":"309","os_version":"macOS 15.5 (24F74)","roots_installed":0,"name":"sensuser","incident_id":"A63798A1-AE09-43B9-BB90-6C79D25C30F7"}
{
  "uptime" : 2900,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "Mac14,13",
  "coalitionID" : 1326,
  "osVersion" : {
    "train" : "macOS 15.5",
    "build" : "24F74",
    "releaseType" : "User"
  },
  "captureTime" : "2025-06-08 10:29:57.4920 -0400",
  "codeSigningMonitor" : 1,
  "incident" : "A63798A1-AE09-43B9-BB90-6C79D25C30F7",
  "pid" : 17087,
  "translated" : false,
  "cpuType" : "ARM-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-06-08 10:26:36.2900 -0400",
  "procStartAbsTime" : 65959201624,
  "procExitAbsTime" : 70787845700,
  "procName" : "sensuser",
  "procPath" : "\/Users\/<USER>\/Desktop\/*\/sensuser.app\/Contents\/MacOS\/sensuser",
  "bundleInfo" : {"CFBundleIdentifier":"com.andrewjosephsmith.noodlenet-cpp.sensuser"},
  "storeInfo" : {"deviceIdentifierForVendor":"98B9DC38-D7F1-5C9B-AA6B-5EAF673BB999","thirdParty":true},
  "parentProc" : "launchd",
  "parentPid" : 1,
  "coalitionName" : "com.andrewjosephsmith.noodlenet-cpp.sensuser",
  "crashReporterKey" : "43D83CEF-E391-39B0-2A34-C64877BFD9C6",
  "appleIntelligenceStatus" : {"state":"available"},
  "codeSigningID" : "sensuser",
  "codeSigningTeamID" : "",
  "codeSigningFlags" : 570556929,
  "codeSigningValidationCategory" : 10,
  "codeSigningTrustLevel" : 4294967295,
  "codeSigningAuxiliaryInfo" : 0,
  "instructionByteStream" : {"beforePC":"fyMD1f17v6n9AwCRm+D\/l78DAJH9e8Go\/w9f1sADX9YQKYDSARAA1A==","atPC":"AwEAVH8jA9X9e7+p\/QMAkZDg\/5e\/AwCR\/XvBqP8PX9bAA1\/WcAqA0g=="},
  "bootSessionUUID" : "C893E51D-244E-43DE-A8A6-6628CC45F14E",
  "sip" : "enabled",
  "exception" : {"codes":"0x0000000000000000, 0x0000000000000000","rawCodes":[0,0],"type":"EXC_CRASH","signal":"SIGABRT"},
  "termination" : {"flags":0,"code":6,"namespace":"SIGNAL","indicator":"Abort trap: 6","byProc":"sensuser","byPid":17087},
  "asi" : {"libsystem_c.dylib":["abort() called"]},
  "extMods" : {"caller":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"system":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"targeted":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"warnings":0},
  "faultingThread" : 1,
  "threads" : [{"id":77488,"threadState":{"x":[{"value":512},{"value":512},{"value":229},{"value":6094883024},{"value":5218023424},{"value":1},{"value":2048},{"value":12},{"value":240},{"value":5512135872},{"value":5218025472},{"value":896},{"value":14},{"value":240},{"value":481},{"value":5218026388},{"value":4},{"value":5512146112},{"value":0},{"value":5512134720},{"value":5780558816},{"value":225},{"value":928},{"value":5511676800},{"value":240},{"value":5512134656},{"value":224},{"value":8542953216,"symbolLocation":0,"symbol":"_main_thread"},{"value":240}],"flavor":"ARM_THREAD_STATE64","lr":{"value":288},"cpsr":{"value":2147487744},"fp":{"value":6094883056},"sp":{"value":6094882816},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4372110340},"far":{"value":0}},"queue":"com.apple.main-thread","frames":[{"imageOffset":121860,"sourceLine":167,"sourceFile":"GeneralMatrixVector.h","symbol":"Eigen::internal::general_matrix_vector_product<long, float, Eigen::internal::const_blas_data_mapper<float, long, 0>, 0, false, float, Eigen::internal::const_blas_data_mapper<float, long, 1>, false, 0>::run(long, long, Eigen::internal::const_blas_data_mapper<float, long, 0> const&, Eigen::internal::const_blas_data_mapper<float, long, 1> const&, float*, long, float)","imageIndex":0,"symbolLocation":428},{"symbol":"void Eigen::internal::generic_product_impl_base<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::internal::generic_product_impl<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::DenseShape, Eigen::DenseShape, 7>>::scaleAndAddTo<Eigen::Matrix<float, -1, 1, 0, -1, 1>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::Matrix<float, -1, -1, 0, -1, -1> const&, Eigen::Matrix<float, -1, 1, 0, -1, 1> const&, float const&)","inline":true,"imageIndex":0,"imageOffset":120532,"symbolLocation":16,"sourceLine":361,"sourceFile":"ProductEvaluators.h"},{"symbol":"void Eigen::internal::generic_product_impl_base<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::internal::generic_product_impl<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::DenseShape, Eigen::DenseShape, 7>>::evalTo<Eigen::Matrix<float, -1, 1, 0, -1, 1>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::Matrix<float, -1, -1, 0, -1, -1> const&, Eigen::Matrix<float, -1, 1, 0, -1, 1> const&)","inline":true,"imageIndex":0,"imageOffset":120532,"symbolLocation":16,"sourceLine":349,"sourceFile":"ProductEvaluators.h"},{"symbol":"Eigen::internal::product_evaluator<Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0>, 7, Eigen::DenseShape, Eigen::DenseShape, float, float>::product_evaluator(Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const&)","inline":true,"imageIndex":0,"imageOffset":120532,"symbolLocation":100,"sourceLine":124,"sourceFile":"ProductEvaluators.h"},{"symbol":"Eigen::internal::evaluator<Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0>>::evaluator(Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const&)","inline":true,"imageIndex":0,"imageOffset":120532,"symbolLocation":100,"sourceLine":35,"sourceFile":"ProductEvaluators.h"},{"symbol":"Eigen::internal::evaluator<Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const>::evaluator(Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const&)","inline":true,"imageIndex":0,"imageOffset":120532,"symbolLocation":100,"sourceLine":104,"sourceFile":"CoreEvaluators.h"},{"symbol":"Eigen::internal::evaluator<Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const>::evaluator(Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const&)","inline":true,"imageIndex":0,"imageOffset":120532,"symbolLocation":100,"sourceLine":104,"sourceFile":"CoreEvaluators.h"},{"symbol":"Eigen::internal::binary_evaluator<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::IndexBased, Eigen::internal::IndexBased, float, float>::Data::Data(Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&)","inline":true,"imageIndex":0,"imageOffset":120532,"symbolLocation":100,"sourceLine":800,"sourceFile":"CoreEvaluators.h"},{"symbol":"Eigen::internal::binary_evaluator<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::IndexBased, Eigen::internal::IndexBased, float, float>::Data::Data(Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&)","inline":true,"imageIndex":0,"imageOffset":120532,"symbolLocation":100,"sourceLine":800,"sourceFile":"CoreEvaluators.h"},{"symbol":"Eigen::internal::binary_evaluator<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::IndexBased, Eigen::internal::IndexBased, float, float>::binary_evaluator(Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&)","inline":true,"imageIndex":0,"imageOffset":120532,"symbolLocation":100,"sourceLine":758,"sourceFile":"CoreEvaluators.h"},{"symbol":"Eigen::internal::evaluator<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>::evaluator(Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&)","inline":true,"imageIndex":0,"imageOffset":120532,"symbolLocation":100,"sourceLine":729,"sourceFile":"CoreEvaluators.h"},{"symbol":"Eigen::internal::evaluator<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>::evaluator(Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&)","inline":true,"imageIndex":0,"imageOffset":120532,"symbolLocation":100,"sourceLine":729,"sourceFile":"CoreEvaluators.h"},{"imageOffset":120532,"sourceLine":774,"sourceFile":"AssignEvaluator.h","symbol":"void Eigen::internal::call_dense_assignment_loop<Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::assign_op<float, float>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&, Eigen::internal::assign_op<float, float> const&)","imageIndex":0,"symbolLocation":140},{"symbol":"Eigen::internal::Assignment<Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::assign_op<float, float>, Eigen::internal::Dense2Dense, void>::run(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&, Eigen::internal::assign_op<float, float> const&)","inline":true,"imageIndex":0,"imageOffset":108560,"symbolLocation":12,"sourceLine":954,"sourceFile":"AssignEvaluator.h"},{"symbol":"void Eigen::internal::call_assignment_no_alias<Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::assign_op<float, float>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&, Eigen::internal::assign_op<float, float> const&)","inline":true,"imageIndex":0,"imageOffset":108560,"symbolLocation":12,"sourceLine":890,"sourceFile":"AssignEvaluator.h"},{"symbol":"void Eigen::internal::call_assignment<Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>, Eigen::internal::assign_op<float, float>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&, Eigen::internal::assign_op<float, float> const&, Eigen::internal::enable_if<!evaluator_assume_aliasing<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>::value, void*>::type)","inline":true,"imageIndex":0,"imageOffset":108560,"symbolLocation":12,"sourceLine":858,"sourceFile":"AssignEvaluator.h"},{"symbol":"void Eigen::internal::call_assignment<Eigen::Matrix<float, -1, 1, 0, -1, 1>, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>(Eigen::Matrix<float, -1, 1, 0, -1, 1>&, Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const> const&)","inline":true,"imageIndex":0,"imageOffset":108560,"symbolLocation":12,"sourceLine":836,"sourceFile":"AssignEvaluator.h"},{"symbol":"Eigen::Matrix<float, -1, 1, 0, -1, 1>& Eigen::PlainObjectBase<Eigen::Matrix<float, -1, 1, 0, -1, 1>>::_set<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>(Eigen::DenseBase<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>> const&)","inline":true,"imageIndex":0,"imageOffset":108560,"symbolLocation":12,"sourceLine":779,"sourceFile":"PlainObjectBase.h"},{"symbol":"Eigen::Matrix<float, -1, 1, 0, -1, 1>& Eigen::Matrix<float, -1, 1, 0, -1, 1>::operator=<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>>(Eigen::DenseBase<Eigen::CwiseBinaryOp<Eigen::internal::scalar_sum_op<float, float>, Eigen::Product<Eigen::Matrix<float, -1, -1, 0, -1, -1>, Eigen::Matrix<float, -1, 1, 0, -1, 1>, 0> const, Eigen::Matrix<float, -1, 1, 0, -1, 1> const>> const&)","inline":true,"imageIndex":0,"imageOffset":108560,"symbolLocation":12,"sourceLine":225,"sourceFile":"Matrix.h"},{"imageOffset":108560,"sourceLine":89,"sourceFile":"layer.cpp","symbol":"Layer::forward(Eigen::Matrix<float, -1, 1, 0, -1, 1> const&)","imageIndex":0,"symbolLocation":356},{"imageOffset":81228,"sourceLine":75,"sourceFile":"mlp.cpp","symbol":"MLP::forward(Eigen::Matrix<float, -1, 1, 0, -1, 1> const&)","imageIndex":0,"symbolLocation":92},{"imageOffset":42096,"sourceLine":316,"sourceFile":"mainwindow.cpp","symbol":"MainWindow::updateHiddenLayerVisualization()","imageIndex":0,"symbolLocation":120},{"imageOffset":40932,"sourceLine":248,"sourceFile":"mainwindow.cpp","symbol":"MainWindow::updateLayerVisualizations()","imageIndex":0,"symbolLocation":56},{"imageOffset":958976,"imageIndex":6},{"imageOffset":2118724,"imageIndex":3},{"imageOffset":958900,"imageIndex":6},{"imageOffset":1995212,"symbol":"QTabBar::setCurrentIndex(int)","symbolLocation":448,"imageIndex":3},{"imageOffset":2011240,"symbol":"QTabBar::mouseReleaseEvent(QMouseEvent*)","symbolLocation":452,"imageIndex":3},{"imageOffset":380700,"symbol":"QWidget::event(QEvent*)","symbolLocation":132,"imageIndex":3},{"imageOffset":2003160,"symbol":"QTabBar::event(QEvent*)","symbolLocation":800,"imageIndex":3},{"imageOffset":64628,"symbol":"QApplicationPrivate::notify_helper(QObject*, QEvent*)","symbolLocation":332,"imageIndex":3},{"imageOffset":73200,"symbol":"QApplication::notify(QObject*, QEvent*)","symbolLocation":5032,"imageIndex":3},{"imageOffset":626916,"symbol":"QCoreApplication::sendSpontaneousEvent(QObject*, QEvent*)","symbolLocation":176,"imageIndex":6},{"imageOffset":66252,"symbol":"QApplicationPrivate::sendMouseEvent(QWidget*, QMouseEvent*, QWidget*, QWidget*, QWidget**, QPointer<QWidget>&, bool, bool)","symbolLocation":892,"imageIndex":3},{"imageOffset":470368,"imageIndex":3},{"imageOffset":466952,"imageIndex":3},{"imageOffset":64628,"symbol":"QApplicationPrivate::notify_helper(QObject*, QEvent*)","symbolLocation":332,"imageIndex":3},{"imageOffset":68684,"symbol":"QApplication::notify(QObject*, QEvent*)","symbolLocation":516,"imageIndex":3},{"imageOffset":626916,"symbol":"QCoreApplication::sendSpontaneousEvent(QObject*, QEvent*)","symbolLocation":176,"imageIndex":6},{"imageOffset":587828,"symbol":"QGuiApplicationPrivate::processMouseEvent(QWindowSystemInterfacePrivate::MouseEvent*)","symbolLocation":1924,"imageIndex":5},{"imageOffset":988280,"symbol":"QWindowSystemInterface::sendWindowSystemEvents(QFlags<QEventLoop::ProcessEventsFlag>)","symbolLocation":408,"imageIndex":5},{"imageOffset":104420,"imageIndex":9},{"imageOffset":511188,"symbol":"__CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__","symbolLocation":28,"imageIndex":13},{"imageOffset":511080,"symbol":"__CFRunLoopDoSource0","symbolLocation":172,"imageIndex":13},{"imageOffset":510420,"symbol":"__CFRunLoopDoSources0","symbolLocation":232,"imageIndex":13},{"imageOffset":505384,"symbol":"__CFRunLoopRun","symbolLocation":840,"imageIndex":13},{"imageOffset":502872,"symbol":"CFRunLoopRunSpecific","symbolLocation":572,"imageIndex":13},{"imageOffset":799356,"symbol":"RunCurrentEventLoopInMode","symbolLocation":324,"imageIndex":14},{"imageOffset":811804,"symbol":"ReceiveNextEventCommon","symbolLocation":216,"imageIndex":14},{"imageOffset":2430084,"symbol":"_BlockUntilNextEventMatchingListInModeWithFilter","symbolLocation":76,"imageIndex":14},{"imageOffset":240308,"symbol":"_DPSNextEvent","symbolLocation":684,"imageIndex":15},{"imageOffset":********,"symbol":"-[NSApplication(NSEventRouting) _nextEventMatchingEventMask:untilDate:inMode:dequeue:]","symbolLocation":688,"imageIndex":15},{"imageOffset":187492,"symbol":"-[NSApplication run]","symbolLocation":480,"imageIndex":15},{"imageOffset":94312,"imageIndex":9},{"imageOffset":665212,"symbol":"QEventLoop::exec(QFlags<QEventLoop::ProcessEventsFlag>)","symbolLocation":588,"imageIndex":6},{"imageOffset":625864,"symbol":"QCoreApplication::exec()","symbolLocation":228,"imageIndex":6},{"imageOffset":20188,"sourceLine":22,"sourceFile":"main.cpp","symbol":"main","imageIndex":0,"symbolLocation":596},{"imageOffset":27544,"symbol":"start","symbolLocation":6076,"imageIndex":16}]},{"triggered":true,"id":77529,"name":"QThread","threadState":{"x":[{"value":0},{"value":0},{"value":0},{"value":0},{"value":115},{"value":46},{"value":1},{"value":**********},{"value":4300427558014982985},{"value":4300427561520098121},{"value":10},{"value":0},{"value":49},{"value":49},{"value":11400714819323198549},{"value":**********},{"value":328},{"value":8560058280},{"value":0},{"value":6},{"value":42755},{"value":6232535264},{"value":6694504818},{"value":6232530272},{"value":0},{"value":0},{"value":6094896040},{"value":6232535040},{"value":16}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6696347788},"cpsr":{"value":1073745920},"fp":{"value":6232528560},"sp":{"value":6232528528},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6696113032,"matchesCrashFrame":1},"far":{"value":0}},"frames":[{"imageOffset":37768,"symbol":"__pthread_kill","symbolLocation":8,"imageIndex":18},{"imageOffset":26764,"symbol":"pthread_kill","symbolLocation":296,"imageIndex":19},{"imageOffset":494688,"symbol":"abort","symbolLocation":124,"imageIndex":20},{"imageOffset":57716,"symbol":"malloc_vreport","symbolLocation":892,"imageIndex":21},{"imageOffset":236032,"symbol":"malloc_zone_error","symbolLocation":100,"imageIndex":21},{"imageOffset":111952,"symbol":"<deduplicated_symbol>","symbolLocation":40,"imageIndex":21},{"symbol":"Eigen::internal::aligned_free(void*)","inline":true,"imageIndex":0,"imageOffset":108604,"symbolLocation":8,"sourceLine":203,"sourceFile":"Memory.h"},{"symbol":"void Eigen::internal::conditional_aligned_free<true>(void*)","inline":true,"imageIndex":0,"imageOffset":108604,"symbolLocation":8,"sourceLine":259,"sourceFile":"Memory.h"},{"symbol":"void Eigen::internal::conditional_aligned_delete_auto<float, true>(float*, unsigned long)","inline":true,"imageIndex":0,"imageOffset":108604,"symbolLocation":8,"sourceLine":446,"sourceFile":"Memory.h"},{"symbol":"Eigen::DenseStorage<float, -1, -1, 1, 0>::~DenseStorage()","inline":true,"imageIndex":0,"imageOffset":108604,"symbolLocation":8,"sourceLine":621,"sourceFile":"DenseStorage.h"},{"symbol":"Eigen::DenseStorage<float, -1, -1, 1, 0>::~DenseStorage()","inline":true,"imageIndex":0,"imageOffset":108604,"symbolLocation":8,"sourceLine":621,"sourceFile":"DenseStorage.h"},{"symbol":"Eigen::PlainObjectBase<Eigen::Matrix<float, -1, 1, 0, -1, 1>>::~PlainObjectBase()","inline":true,"imageIndex":0,"imageOffset":108604,"symbolLocation":8,"sourceLine":98,"sourceFile":"PlainObjectBase.h"},{"symbol":"Eigen::Matrix<float, -1, 1, 0, -1, 1>::~Matrix()","inline":true,"imageIndex":0,"imageOffset":108604,"symbolLocation":8,"sourceLine":178,"sourceFile":"Matrix.h"},{"symbol":"Eigen::Matrix<float, -1, 1, 0, -1, 1>::~Matrix()","inline":true,"imageIndex":0,"imageOffset":108604,"symbolLocation":8,"sourceLine":178,"sourceFile":"Matrix.h"},{"imageOffset":108604,"sourceLine":92,"sourceFile":"layer.cpp","symbol":"Layer::forward(Eigen::Matrix<float, -1, 1, 0, -1, 1> const&)","imageIndex":0,"symbolLocation":400},{"imageOffset":81228,"sourceLine":75,"sourceFile":"mlp.cpp","symbol":"MLP::forward(Eigen::Matrix<float, -1, 1, 0, -1, 1> const&)","imageIndex":0,"symbolLocation":92},{"imageOffset":81384,"sourceLine":91,"sourceFile":"mlp.cpp","symbol":"MLP::train(Eigen::Matrix<float, -1, 1, 0, -1, 1> const&, Eigen::Matrix<float, -1, 1, 0, -1, 1> const&, float, OptimizerType, int)","imageIndex":0,"symbolLocation":72},{"imageOffset":138236,"sourceLine":212,"sourceFile":"trainingworker.cpp","symbol":"TrainingWorker::train()","imageIndex":0,"symbolLocation":2256},{"imageOffset":926276,"symbol":"QObject::event(QEvent*)","symbolLocation":672,"imageIndex":6},{"imageOffset":64628,"symbol":"QApplicationPrivate::notify_helper(QObject*, QEvent*)","symbolLocation":332,"imageIndex":3},{"imageOffset":68684,"symbol":"QApplication::notify(QObject*, QEvent*)","symbolLocation":516,"imageIndex":3},{"imageOffset":626616,"symbol":"QCoreApplication::sendEvent(QObject*, QEvent*)","symbolLocation":172,"imageIndex":6},{"imageOffset":628792,"symbol":"QCoreApplicationPrivate::sendPostedEvents(QObject*, int, QThreadData*)","symbolLocation":524,"imageIndex":6},{"imageOffset":2255024,"symbol":"QEventDispatcherUNIX::processEvents(QFlags<QEventLoop::ProcessEventsFlag>)","symbolLocation":84,"imageIndex":6},{"imageOffset":665212,"symbol":"QEventLoop::exec(QFlags<QEventLoop::ProcessEventsFlag>)","symbolLocation":588,"imageIndex":6},{"imageOffset":1634128,"symbol":"QThread::exec()","symbolLocation":332,"imageIndex":6},{"imageOffset":2246716,"imageIndex":6},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":19},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":19}]},{"id":77539,"name":"com.apple.NSEventThread","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":263947215175680},{"value":0},{"value":263947215175680},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":61455},{"value":0},{"value":18446744073709551569},{"value":8560060088},{"value":0},{"value":4294967295},{"value":2},{"value":263947215175680},{"value":0},{"value":263947215175680},{"value":6234251400},{"value":8589934592},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6696154016},"cpsr":{"value":4096},"fp":{"value":6234251248},"sp":{"value":6234251168},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6696078388},"far":{"value":0}},"frames":[{"imageOffset":3124,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":18},{"imageOffset":78752,"symbol":"mach_msg2_internal","symbolLocation":76,"imageIndex":18},{"imageOffset":38756,"symbol":"mach_msg_overwrite","symbolLocation":484,"imageIndex":18},{"imageOffset":4008,"symbol":"mach_msg","symbolLocation":24,"imageIndex":18},{"imageOffset":511612,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":160,"imageIndex":13},{"imageOffset":505752,"symbol":"__CFRunLoopRun","symbolLocation":1208,"imageIndex":13},{"imageOffset":502872,"symbol":"CFRunLoopRunSpecific","symbolLocation":572,"imageIndex":13},{"imageOffset":1435644,"symbol":"_NSEventThread","symbolLocation":140,"imageIndex":15},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":19},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":19}]},{"id":79798,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":19}],"threadState":{"x":[{"value":6097170432},{"value":49707},{"value":6096633856},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6097170432},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6696328044},"far":{"value":0}}},{"id":79830,"name":"Thread (pooled)","threadState":{"x":[{"value":260},{"value":0},{"value":5632},{"value":0},{"value":0},{"value":160},{"value":29},{"value":999999000},{"value":6233107800},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8560058208},{"value":0},{"value":105553140589184},{"value":105553140589248},{"value":6233108704},{"value":999999000},{"value":29},{"value":5632},{"value":5633},{"value":5888},{"value":18446744072709551616},{"value":1}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6696349920},"cpsr":{"value":1610616832},"fp":{"value":6233107920},"sp":{"value":6233107776},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6696092620},"far":{"value":0}},"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":18},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":19},{"imageOffset":2303192,"imageIndex":6},{"imageOffset":2302824,"symbol":"QWaitCondition::wait(QMutex*, QDeadlineTimer)","symbolLocation":108,"imageIndex":6},{"imageOffset":2279712,"imageIndex":6},{"imageOffset":2246716,"imageIndex":6},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":19},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":19}]},{"id":79831,"name":"Thread (pooled)","threadState":{"x":[{"value":260},{"value":0},{"value":9216},{"value":0},{"value":0},{"value":160},{"value":30},{"value":0},{"value":6233681240},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8560058208},{"value":0},{"value":105553140690688},{"value":105553140690752},{"value":6233682144},{"value":0},{"value":30},{"value":9216},{"value":9217},{"value":9472},{"value":18446744072709551616},{"value":1}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6696349920},"cpsr":{"value":1610616832},"fp":{"value":6233681360},"sp":{"value":6233681216},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6696092620},"far":{"value":0}},"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":18},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":19},{"imageOffset":2303192,"imageIndex":6},{"imageOffset":2302824,"symbol":"QWaitCondition::wait(QMutex*, QDeadlineTimer)","symbolLocation":108,"imageIndex":6},{"imageOffset":2279712,"imageIndex":6},{"imageOffset":2246716,"imageIndex":6},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":19},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":19}]},{"id":79832,"name":"Thread (pooled)","threadState":{"x":[{"value":260},{"value":0},{"value":9216},{"value":0},{"value":0},{"value":160},{"value":29},{"value":999999000},{"value":6234828120},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8560058208},{"value":0},{"value":105553140691072},{"value":105553140691136},{"value":6234829024},{"value":999999000},{"value":29},{"value":9216},{"value":9217},{"value":9472},{"value":18446744072709551616},{"value":1}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6696349920},"cpsr":{"value":1610616832},"fp":{"value":6234828240},"sp":{"value":6234828096},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6696092620},"far":{"value":0}},"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":18},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":19},{"imageOffset":2303192,"imageIndex":6},{"imageOffset":2302824,"symbol":"QWaitCondition::wait(QMutex*, QDeadlineTimer)","symbolLocation":108,"imageIndex":6},{"imageOffset":2279712,"imageIndex":6},{"imageOffset":2246716,"imageIndex":6},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":19},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":19}]},{"id":79833,"name":"Thread (pooled)","threadState":{"x":[{"value":260},{"value":0},{"value":5632},{"value":0},{"value":0},{"value":160},{"value":29},{"value":999999000},{"value":6235401560},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8560058208},{"value":0},{"value":105553140586112},{"value":105553140586176},{"value":6235402464},{"value":999999000},{"value":29},{"value":5632},{"value":5633},{"value":5888},{"value":18446744072709551616},{"value":1}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6696349920},"cpsr":{"value":1610616832},"fp":{"value":6235401680},"sp":{"value":6235401536},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6696092620},"far":{"value":0}},"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":18},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":19},{"imageOffset":2303192,"imageIndex":6},{"imageOffset":2302824,"symbol":"QWaitCondition::wait(QMutex*, QDeadlineTimer)","symbolLocation":108,"imageIndex":6},{"imageOffset":2279712,"imageIndex":6},{"imageOffset":2246716,"imageIndex":6},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":19},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":19}]},{"id":79904,"name":"Thread (pooled)","threadState":{"x":[{"value":260},{"value":0},{"value":1280},{"value":0},{"value":0},{"value":160},{"value":29},{"value":999998000},{"value":6096022872},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8560058208},{"value":0},{"value":105553141151488},{"value":105553141151552},{"value":6096023776},{"value":999998000},{"value":29},{"value":1280},{"value":1281},{"value":1536},{"value":18446744072709551616},{"value":1}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6696349920},"cpsr":{"value":1610616832},"fp":{"value":6096022992},"sp":{"value":6096022848},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6696092620},"far":{"value":0}},"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":18},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":19},{"imageOffset":2303192,"imageIndex":6},{"imageOffset":2302824,"symbol":"QWaitCondition::wait(QMutex*, QDeadlineTimer)","symbolLocation":108,"imageIndex":6},{"imageOffset":2279712,"imageIndex":6},{"imageOffset":2246716,"imageIndex":6},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":19},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":19}]},{"id":79905,"name":"Thread (pooled)","threadState":{"x":[{"value":260},{"value":0},{"value":1536},{"value":0},{"value":0},{"value":160},{"value":29},{"value":999998000},{"value":6097743192},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8560058208},{"value":0},{"value":105553141144192},{"value":105553141144256},{"value":6097744096},{"value":999998000},{"value":29},{"value":1536},{"value":1537},{"value":1792},{"value":18446744072709551616},{"value":1}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6696349920},"cpsr":{"value":1610616832},"fp":{"value":6097743312},"sp":{"value":6097743168},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6696092620},"far":{"value":0}},"frames":[{"imageOffset":17356,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":18},{"imageOffset":28896,"symbol":"_pthread_cond_wait","symbolLocation":984,"imageIndex":19},{"imageOffset":2303192,"imageIndex":6},{"imageOffset":2302824,"symbol":"QWaitCondition::wait(QMutex*, QDeadlineTimer)","symbolLocation":108,"imageIndex":6},{"imageOffset":2279712,"imageIndex":6},{"imageOffset":2246716,"imageIndex":6},{"imageOffset":27660,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":19},{"imageOffset":7040,"symbol":"thread_start","symbolLocation":8,"imageIndex":19}]},{"id":79924,"frames":[{"imageOffset":7020,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":19}],"threadState":{"x":[{"value":6235975680},{"value":0},{"value":6235439104},{"value":0},{"value":278532},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6235975680},"esr":{"value":0,"description":" Address size fault"},"pc":{"value":6696328044},"far":{"value":0}}}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4371988480,
    "CFBundleIdentifier" : "com.andrewjosephsmith.noodlenet-cpp.sensuser",
    "size" : 196608,
    "uuid" : "68d9e804-55bf-34cd-830c-87e9d4bdbb04",
    "path" : "\/Users\/<USER>\/Desktop\/*\/sensuser.app\/Contents\/MacOS\/sensuser",
    "name" : "sensuser"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4376477696,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "org.qt-project.QtCharts",
    "size" : 1015808,
    "uuid" : "95df4f4e-d558-3602-afb9-42424d803857",
    "path" : "\/opt\/Qt\/*\/QtCharts.framework\/Versions\/A\/QtCharts",
    "name" : "QtCharts",
    "CFBundleVersion" : "6.9.1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4372627456,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "org.qt-project.QtOpenGLWidgets",
    "size" : 49152,
    "uuid" : "21309296-564a-3214-894b-5cb50594905b",
    "path" : "\/opt\/Qt\/*\/QtOpenGLWidgets.framework\/Versions\/A\/QtOpenGLWidgets",
    "name" : "QtOpenGLWidgets",
    "CFBundleVersion" : "6.9.1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4390862848,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "org.qt-project.QtWidgets",
    "size" : 4636672,
    "uuid" : "e84cfcf9-d145-312e-885c-159d40dfbd9e",
    "path" : "\/opt\/Qt\/*\/QtWidgets.framework\/Versions\/A\/QtWidgets",
    "name" : "QtWidgets",
    "CFBundleVersion" : "6.9.1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4374052864,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "org.qt-project.QtOpenGL",
    "size" : 425984,
    "uuid" : "a5c4c691-be77-3d0a-a711-c0da2c7ebbe5",
    "path" : "\/opt\/Qt\/*\/QtOpenGL.framework\/Versions\/A\/QtOpenGL",
    "name" : "QtOpenGL",
    "CFBundleVersion" : "6.9.1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4378198016,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "org.qt-project.QtGui",
    "size" : 6946816,
    "uuid" : "4f544aad-06c2-3315-8261-1c9e3a7e3082",
    "path" : "\/opt\/Qt\/*\/QtGui.framework\/Versions\/A\/QtGui",
    "name" : "QtGui",
    "CFBundleVersion" : "6.9.1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4409131008,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "org.qt-project.QtCore",
    "size" : 4931584,
    "uuid" : "115f06be-4c99-3fa9-8699-1605ab53b730",
    "path" : "\/opt\/Qt\/*\/QtCore.framework\/Versions\/A\/QtCore",
    "name" : "QtCore",
    "CFBundleVersion" : "6.9.1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4372758528,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "org.qt-project.QtDBus",
    "size" : 540672,
    "uuid" : "bd131634-af8c-3140-b3ef-21475baa7c1d",
    "path" : "\/opt\/Qt\/*\/QtDBus.framework\/Versions\/A\/QtDBus",
    "name" : "QtDBus",
    "CFBundleVersion" : "6.9.1"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4375134208,
    "CFBundleShortVersionString" : "3.0",
    "CFBundleIdentifier" : "com.apple.security.csparser",
    "size" : 131072,
    "uuid" : "c12848ee-0663-3987-842f-8832599d139f",
    "path" : "\/System\/Library\/Frameworks\/Security.framework\/Versions\/A\/PlugIns\/csparser.bundle\/Contents\/MacOS\/csparser",
    "name" : "csparser",
    "CFBundleVersion" : "61439.120.27"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4389486592,
    "size" : 688128,
    "uuid" : "7cdd975f-1e6c-316b-a344-dec675d31e1b",
    "path" : "\/opt\/Qt\/*\/libqcocoa.dylib",
    "name" : "libqcocoa.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4405542912,
    "size" : 49152,
    "uuid" : "d02a05cb-6440-3e7e-a02f-931734cab666",
    "path" : "\/usr\/lib\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4630921216,
    "size" : 147456,
    "uuid" : "e337df1c-6c8f-3c8f-801b-d23394f34e54",
    "path" : "\/opt\/Qt\/*\/libqmacstyle.dylib",
    "name" : "libqmacstyle.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4654514176,
    "CFBundleShortVersionString" : "327.5",
    "CFBundleIdentifier" : "com.apple.AGXMetalG14X",
    "size" : 7372800,
    "uuid" : "da19826d-64c0-3f82-97cc-7ae98b831d91",
    "path" : "\/System\/Library\/Extensions\/AGXMetalG14X.bundle\/Contents\/MacOS\/AGXMetalG14X",
    "name" : "AGXMetalG14X",
    "CFBundleVersion" : "327.5"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6696800256,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.CoreFoundation",
    "size" : 5500928,
    "uuid" : "df489a59-b4f6-32b8-9bb4-9b832960aa52",
    "path" : "\/System\/Library\/Frameworks\/CoreFoundation.framework\/Versions\/A\/CoreFoundation",
    "name" : "CoreFoundation",
    "CFBundleVersion" : "3502.1.401"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6892146688,
    "CFBundleShortVersionString" : "2.1.1",
    "CFBundleIdentifier" : "com.apple.HIToolbox",
    "size" : 3174368,
    "uuid" : "9286e29f-fcee-31d0-acea-2842ea23bedf",
    "path" : "\/System\/Library\/Frameworks\/Carbon.framework\/Versions\/A\/Frameworks\/HIToolbox.framework\/Versions\/A\/HIToolbox",
    "name" : "HIToolbox"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6763286528,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.AppKit",
    "size" : 21568640,
    "uuid" : "5d0da1bd-412c-3ed8-84e9-40ca62fe7b42",
    "path" : "\/System\/Library\/Frameworks\/AppKit.framework\/Versions\/C\/AppKit",
    "name" : "AppKit",
    "CFBundleVersion" : "2575.60.5"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6692519936,
    "size" : 636112,
    "uuid" : "9cf0401a-a938-389e-a77d-9e9608076ccf",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "size" : 0,
    "source" : "A",
    "base" : 0,
    "uuid" : "00000000-0000-0000-0000-000000000000"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6696075264,
    "size" : 243284,
    "uuid" : "60485b6f-67e5-38c1-aec9-efd6031ff166",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6696321024,
    "size" : 51784,
    "uuid" : "647b91fc-96d3-3bbb-af08-970df45257c8",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6694842368,
    "size" : 529520,
    "uuid" : "f4529d5e-24f3-3bbb-bd3c-984856875fc8",
    "path" : "\/usr\/lib\/system\/libsystem_c.dylib",
    "name" : "libsystem_c.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6694248448,
    "size" : 290800,
    "uuid" : "e2c4cbe4-6195-3328-b87c-2dfa4a6ad039",
    "path" : "\/usr\/lib\/system\/libsystem_malloc.dylib",
    "name" : "libsystem_malloc.dylib"
  }
],
  "sharedCache" : {
  "base" : 6691684352,
  "size" : 5047205888,
  "uuid" : "d7397d7f-8df9-3920-81a7-c0a144be9c51"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=1.7G resident=0K(0%) swapped_out_or_unallocated=1.7G(100%)\nWritable regions: Total=4.5G written=2104K(0%) resident=2104K(0%) swapped_out=0K(0%) unallocated=4.5G(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nAccelerate framework               256K        2 \nActivity Tracing                   256K        1 \nCG image                           208K       11 \nColorSync                          640K       29 \nCoreAnimation                      464K       29 \nCoreGraphics                        48K        3 \nCoreUI image data                 1760K       22 \nFoundation                          16K        1 \nKernel Alloc Once                   32K        1 \nMALLOC                             4.5G       99 \nMALLOC guard page                  288K       18 \nSTACK GUARD                       56.2M       11 \nStack                             13.3M       12 \nVM_ALLOCATE                        256K       16 \n__AUTH                            5367K      683 \n__AUTH_CONST                      75.9M      924 \n__CTF                               824        1 \n__DATA                            25.5M      917 \n__DATA_CONST                      27.3M      944 \n__DATA_DIRTY                      2763K      335 \n__FONT_DATA                        2352        1 \n__INFO_FILTER                         8        1 \n__LINKEDIT                       621.4M       14 \n__OBJC_RO                         61.4M        1 \n__OBJC_RW                         2396K        1 \n__TEXT                             1.1G      964 \n__TPRO_CONST                       128K        2 \nmapped file                      224.6M       29 \npage table in kernel              2104K        1 \nshared memory                     1392K       14 \n===========                     =======  ======= \nTOTAL                              6.7G     5087 \n",
  "legacyInfo" : {
  "threadTriggered" : {
    "name" : "QThread"
  }
},
  "logWritingSignature" : "5ee07a4cd2c610e8fd1b584e92c3ac96fb12725d",
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "64b21a7351cbb02ce3442e4e",
      "factorPackIds" : {
        "REMINDERS_GROCERY" : "6647f0f7b6a75d3dc32993e7"
      },
      "deploymentId" : 240000042
    },
    {
      "rolloutId" : "61675b89201f677a9a4cbd65",
      "factorPackIds" : {
        "HEALTH_FEATURE_AVAILABILITY" : "65a855f5f087695cfac03d1f"
      },
      "deploymentId" : 240000187
    }
  ],
  "experiments" : [

  ]
}
}

Model: Mac14,13, BootROM 11881.121.1, proc 12:8:4 processors, 64 GB, SMC 
Graphics: Apple M2 Max, Apple M2 Max, Built-In
Display: SMS27A350H, 1920 x 1080 (1080p FHD - Full High Definition), Main, MirrorOff, Online
Display: VX2739wm, 1920 x 1080 (1080p FHD - Full High Definition), MirrorOff, Online
Memory Module: LPDDR5, Hynix
AirPort: spairport_wireless_card_type_wifi (0x14E4, 0x4388), wl0: Mar 22 2025 02:16:34 version **********.41.51.180 FWID 01-8d4e53ab
IO80211_driverkit-1475.39 "IO80211_driverkit-1475.39" Apr 18 2025 20:10:40
AirPort: 
Bluetooth: Version (null), 0 services, 0 devices, 0 incoming serial ports
Network Service: Wi-Fi, AirPort, en1
Network Service: Ethernet, Ethernet, en0
USB Device: USB31Bus
USB Device: G-DRIVE mobile SSD R-Series
USB Device: USB31Bus
USB Device: USB31Bus
USB Device: USB3.0 Hub
USB Device: USB2.0 Hub
USB Device: DELL USB Keyboard
USB Device: USB-C VGA Multiport Adapter
USB Device: USB31Bus
USB Device: AKG C44-USB Microphone
USB Device: USB31Bus
USB Device: USB2 Hub
USB Device: Microsoft Pro Intellimouse
USB Device: Apple Watch Magnetic Charging Cable
USB Device: USB3 Gen2 Hub
Thunderbolt Bus: Mac Studio, Apple Inc.
Thunderbolt Bus: Mac Studio, Apple Inc.
Thunderbolt Bus: Mac Studio, Apple Inc.
Thunderbolt Bus: Mac Studio, Apple Inc.
